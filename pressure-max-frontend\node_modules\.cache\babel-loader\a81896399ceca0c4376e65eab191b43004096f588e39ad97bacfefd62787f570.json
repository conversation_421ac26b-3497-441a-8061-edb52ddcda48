{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Paintbrush2 = createLucideIcon(\"Paintbrush2\", [[\"path\", {\n  d: \"M14 19.9V16h3a2 2 0 0 0 2-2v-2H5v2c0 1.1.9 2 2 2h3v3.9a2 2 0 1 0 4 0Z\",\n  key: \"1c8kta\"\n}], [\"path\", {\n  d: \"M6 12V2h12v10\",\n  key: \"1esbnf\"\n}], [\"path\", {\n  d: \"M14 2v4\",\n  key: \"qmzblu\"\n}], [\"path\", {\n  d: \"M10 2v2\",\n  key: \"7u0qdc\"\n}]]);\nexport { Paintbrush2 as default };", "map": {"version": 3, "names": ["Paintbrush2", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\node_modules\\lucide-react\\src\\icons\\paintbrush-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Paintbrush2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMTkuOVYxNmgzYTIgMiAwIDAgMCAyLTJ2LTJINXYyYzAgMS4xLjkgMiAyIDJoM3YzLjlhMiAyIDAgMSAwIDQgMFoiIC8+CiAgPHBhdGggZD0iTTYgMTJWMmgxMnYxMCIgLz4KICA8cGF0aCBkPSJNMTQgMnY0IiAvPgogIDxwYXRoIGQ9Ik0xMCAydjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/paintbrush-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Paintbrush2 = createLucideIcon('Paintbrush2', [\n  [\n    'path',\n    { d: 'M14 19.9V16h3a2 2 0 0 0 2-2v-2H5v2c0 1.1.9 2 2 2h3v3.9a2 2 0 1 0 4 0Z', key: '1c8kta' },\n  ],\n  ['path', { d: 'M6 12V2h12v10', key: '1esbnf' }],\n  ['path', { d: 'M14 2v4', key: 'qmzblu' }],\n  ['path', { d: 'M10 2v2', key: '7u0qdc' }],\n]);\n\nexport default Paintbrush2;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CACE,QACA;EAAEC,CAAA,EAAG,uEAAyE;EAAAC,GAAA,EAAK;AAAS,EAC9F,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}