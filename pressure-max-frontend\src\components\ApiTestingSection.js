import React, { useState, useEffect } from 'react';
import { userAPI, healthCheck } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';
import { Activity, User, Server, AlertCircle, CheckCircle, Clock } from 'lucide-react';

const ApiTestingSection = () => {
  const { isAuthenticated, user } = useAuth();
  const [apiLogs, setApiLogs] = useState([]);
  const [serverHealth, setServerHealth] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState({
    health: false,
    profile: false
  });

  useEffect(() => {
    checkServerHealth();
    if (isAuthenticated) {
      loadUserProfile();
    }
  }, [isAuthenticated]);

  const addApiLog = (method, endpoint, status, response, error = null) => {
    const log = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      method,
      endpoint,
      status,
      response,
      error,
      success: status >= 200 && status < 300
    };
    
    setApiLogs(prev => [log, ...prev.slice(0, 19)]); // Keep last 20 logs
  };

  const checkServerHealth = async () => {
    setLoading(prev => ({ ...prev, health: true }));
    try {
      const response = await healthCheck();
      setServerHealth(response.data);
      addApiLog('GET', '/health', response.status, response.data);
    } catch (error) {
      setServerHealth(null);
      addApiLog('GET', '/health', error.response?.status || 0, null, error.message);
    } finally {
      setLoading(prev => ({ ...prev, health: false }));
    }
  };

  const loadUserProfile = async () => {
    setLoading(prev => ({ ...prev, profile: true }));
    try {
      const response = await userAPI.getProfile();
      setUserProfile(response.data);
      addApiLog('GET', '/api/v1/users/profile', response.status, response.data);
    } catch (error) {
      setUserProfile(null);
      addApiLog('GET', '/api/v1/users/profile', error.response?.status || 0, null, error.message);
      toast.error('Failed to load user profile');
    } finally {
      setLoading(prev => ({ ...prev, profile: false }));
    }
  };

  const testApiEndpoint = async (endpoint, method = 'GET') => {
    try {
      let response;
      switch (endpoint) {
        case 'health':
          response = await healthCheck();
          break;
        case 'profile':
          response = await userAPI.getProfile();
          break;
        default:
          throw new Error('Unknown endpoint');
      }
      
      addApiLog(method, endpoint, response.status, response.data);
      toast.success(`${endpoint} test successful`);
    } catch (error) {
      addApiLog(method, endpoint, error.response?.status || 0, null, error.message);
      toast.error(`${endpoint} test failed: ${error.message}`);
    }
  };

  const clearLogs = () => {
    setApiLogs([]);
    toast.success('API logs cleared');
  };

  return (
    <div className="api-testing-section">
      <h2>API Testing & Monitoring</h2>
      
      {/* Server Health */}
      <div className="health-section">
        <div className="section-header">
          <h3>
            <Server size={16} />
            Server Health
          </h3>
          <button 
            onClick={checkServerHealth}
            disabled={loading.health}
            className="test-btn"
          >
            {loading.health ? 'Checking...' : 'Check Health'}
          </button>
        </div>
        
        {serverHealth ? (
          <div className="health-status healthy">
            <CheckCircle size={16} />
            <div className="health-details">
              <p><strong>Status:</strong> {serverHealth.status}</p>
              <p><strong>Uptime:</strong> {Math.floor(serverHealth.uptime / 60)} minutes</p>
              <p><strong>Version:</strong> {serverHealth.version}</p>
              <p><strong>Timestamp:</strong> {new Date(serverHealth.timestamp).toLocaleString()}</p>
            </div>
          </div>
        ) : (
          <div className="health-status unhealthy">
            <AlertCircle size={16} />
            <p>Server health check failed or server is offline</p>
          </div>
        )}
      </div>

      {/* User Profile */}
      {isAuthenticated && (
        <div className="profile-section">
          <div className="section-header">
            <h3>
              <User size={16} />
              User Profile
            </h3>
            <button 
              onClick={loadUserProfile}
              disabled={loading.profile}
              className="test-btn"
            >
              {loading.profile ? 'Loading...' : 'Reload Profile'}
            </button>
          </div>
          
          {userProfile ? (
            <div className="profile-data">
              <div className="profile-item">
                <strong>ID:</strong> {userProfile.id}
              </div>
              <div className="profile-item">
                <strong>Name:</strong> {userProfile.firstName} {userProfile.lastName}
              </div>
              <div className="profile-item">
                <strong>Email:</strong> {userProfile.email}
              </div>
              <div className="profile-item">
                <strong>Role:</strong> {userProfile.role}
              </div>
              <div className="profile-item">
                <strong>Active:</strong> {userProfile.isActive ? 'Yes' : 'No'}
              </div>
              <div className="profile-item">
                <strong>Last Login:</strong> {userProfile.lastLoginAt ? 
                  new Date(userProfile.lastLoginAt).toLocaleString() : 'Never'}
              </div>
            </div>
          ) : (
            <div className="no-profile">
              <AlertCircle size={16} />
              <p>Failed to load user profile</p>
            </div>
          )}
        </div>
      )}

      {/* Quick API Tests */}
      <div className="quick-tests">
        <h3>
          <Activity size={16} />
          Quick API Tests
        </h3>
        <div className="test-buttons">
          <button onClick={() => testApiEndpoint('health')} className="test-btn">
            Test Health Endpoint
          </button>
          {isAuthenticated && (
            <button onClick={() => testApiEndpoint('profile')} className="test-btn">
              Test Profile Endpoint
            </button>
          )}
        </div>
      </div>

      {/* API Logs */}
      <div className="api-logs">
        <div className="logs-header">
          <h3>
            <Clock size={16} />
            API Request/Response Logs ({apiLogs.length})
          </h3>
          <button onClick={clearLogs} className="clear-btn">
            Clear Logs
          </button>
        </div>
        
        <div className="logs-container">
          {apiLogs.length > 0 ? (
            apiLogs.map((log) => (
              <div key={log.id} className={`log-entry ${log.success ? 'success' : 'error'}`}>
                <div className="log-header">
                  <span className="log-method">{log.method}</span>
                  <span className="log-endpoint">{log.endpoint}</span>
                  <span className={`log-status status-${Math.floor(log.status / 100)}`}>
                    {log.status}
                  </span>
                  <span className="log-timestamp">
                    {new Date(log.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                
                {log.error && (
                  <div className="log-error">
                    <strong>Error:</strong> {log.error}
                  </div>
                )}
                
                {log.response && (
                  <details className="log-response">
                    <summary>Response Data</summary>
                    <pre>{JSON.stringify(log.response, null, 2)}</pre>
                  </details>
                )}
              </div>
            ))
          ) : (
            <div className="no-logs">
              <p>No API requests logged yet. Interact with the API to see logs here.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApiTestingSection;
