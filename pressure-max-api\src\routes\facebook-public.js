const express = require('express');
const { query, validationResult } = require('express-validator');
const crypto = require('crypto');

const { asyncHandler, ValidationError } = require('../middleware/errorHandler');
const facebookService = require('../services/facebookService');
const logger = require('../config/logger');

const router = express.Router();

/**
 * @swagger
 * /facebook-public/oauth-url:
 *   get:
 *     summary: Get Facebook OAuth URL (Public - No Authentication Required)
 *     tags: [Facebook Public]
 *     parameters:
 *       - in: query
 *         name: redirectUri
 *         required: true
 *         schema:
 *           type: string
 *         description: Redirect URI after OAuth
 *     responses:
 *       200:
 *         description: OAuth URL generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 oauthUrl:
 *                   type: string
 *                 state:
 *                   type: string
 *                 message:
 *                   type: string
 */
router.get('/oauth-url', [
  query('redirectUri').notEmpty().withMessage('Redirect URI is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { redirectUri } = req.query;
  
  // Generate state parameter for security
  const state = crypto.randomBytes(32).toString('hex');
  
  // For public OAuth, we'll store the state in a temporary way
  // Since we don't have a user ID, we'll use the state itself as the key
  try {
    if (req.app.locals.redis && req.app.locals.redis.set) {
      await req.app.locals.redis.set(`public_oauth_state:${state}`, {
        redirectUri,
        timestamp: Date.now()
      }, 600); // 10 minutes
    }
  } catch (error) {
    logger.warn('Could not store OAuth state in Redis:', error.message);
    // Continue without Redis - state validation will be skipped
  }

  const oauthUrl = facebookService.generateOAuthUrl(redirectUri, state);

  logger.info('Public OAuth URL generated', {
    redirectUri,
    state: state.substring(0, 8) + '...' // Log partial state for debugging
  });

  res.json({
    success: true,
    oauthUrl,
    state,
    message: 'OAuth URL generated successfully'
  });
}));

/**
 * @swagger
 * /facebook-public/oauth-callback:
 *   post:
 *     summary: Handle Facebook OAuth callback (Public)
 *     tags: [Facebook Public]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - state
 *             properties:
 *               code:
 *                 type: string
 *               state:
 *                 type: string
 *               redirectUri:
 *                 type: string
 *     responses:
 *       200:
 *         description: OAuth callback processed successfully
 */
router.post('/oauth-callback', [
  query('code').notEmpty().withMessage('Authorization code is required'),
  query('state').notEmpty().withMessage('State parameter is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { code, state, redirectUri } = req.body;

  // Verify state parameter if Redis is available
  try {
    if (req.app.locals.redis && req.app.locals.redis.get) {
      const storedData = await req.app.locals.redis.get(`public_oauth_state:${state}`);
      if (storedData) {
        // Clean up state
        await req.app.locals.redis.del(`public_oauth_state:${state}`);
        logger.info('OAuth state verified and cleaned up');
      } else {
        logger.warn('OAuth state not found in Redis, continuing without verification');
      }
    }
  } catch (error) {
    logger.warn('Could not verify OAuth state:', error.message);
    // Continue without state verification
  }

  try {
    // Exchange code for access token
    const tokenData = await facebookService.exchangeCodeForToken(code, redirectUri);
    
    // Get long-lived token
    const longLivedToken = await facebookService.getLongLivedToken(tokenData.access_token);
    
    // Get user profile
    const profile = await facebookService.getUserProfile(longLivedToken.access_token);
    
    // Get permissions
    const permissions = tokenData.scope ? tokenData.scope.split(',') : [];

    logger.info('Facebook OAuth completed successfully', {
      facebookUserId: profile.id,
      permissions: permissions.length
    });

    res.json({
      success: true,
      message: 'Facebook OAuth completed successfully',
      profile,
      permissions,
      accessToken: longLivedToken.access_token,
      expiresIn: longLivedToken.expires_in
    });
  } catch (error) {
    logger.error('Facebook OAuth callback error:', error);
    throw error;
  }
}));

/**
 * @swagger
 * /facebook-public/test:
 *   get:
 *     summary: Test endpoint for public Facebook routes
 *     tags: [Facebook Public]
 *     responses:
 *       200:
 *         description: Test successful
 */
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Public Facebook routes are working',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
