{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nconst toKebabCase = string => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase().trim();\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    className = \"\",\n    children,\n    ...rest\n  }, ref) => createElement(\"svg\", {\n    ref,\n    ...defaultAttributes,\n    width: size,\n    height: size,\n    stroke: color,\n    strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n    className: [\"lucide\", `lucide-${toKebabCase(iconName)}`, className].join(\" \"),\n    ...rest\n  }, [...iconNode.map(([tag, attrs]) => createElement(tag, attrs)), ...(Array.isArray(children) ? children : [children])]));\n  Component.displayName = `${iconName}`;\n  return Component;\n};\nexport { createLucideIcon as default, toKebabCase };", "map": {"version": 3, "names": ["toKebabCase", "string", "replace", "toLowerCase", "trim", "createLucideIcon", "iconName", "iconNode", "Component", "forwardRef", "color", "size", "strokeWidth", "absoluteStrokeWidth", "className", "children", "rest", "ref", "createElement", "defaultAttributes", "width", "height", "stroke", "Number", "join", "map", "tag", "attrs", "Array", "isArray", "displayName"], "sources": ["C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\node_modules\\lucide-react\\src\\createLucideIcon.ts"], "sourcesContent": ["import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "mappings": ";;;;;;;;;AA6Ba,MAAAA,WAAA,GAAeC,MAAA,IAC1BA,MACG,CAAAC,OAAA,CAAQ,sBAAsB,OAAO,EACrCC,WAAY,GACZC,IAAK;AAEJ,MAAAC,gBAAA,GAAmBA,CAACC,QAAA,EAAkBC,QAAmC;EAC7E,MAAMC,SAAY,GAAAC,UAAA,CAChB,CAAC;IAAEC,KAAQ;IAAgBC,IAAA,GAAO,EAAI;IAAAC,WAAA,GAAc,CAAG;IAAAC,mBAAA;IAAqBC,SAAY;IAAIC,QAAa;IAAA,GAAAC;EAAA,GAAQC,GAC/G,KAAAC,aAAA,CACE,OACA;IACED,GAAA;IACA,GAAGE,iBAAA;IACHC,KAAO,EAAAT,IAAA;IACPU,MAAQ,EAAAV,IAAA;IACRW,MAAQ,EAAAZ,KAAA;IACRE,WAAA,EAAaC,mBAAA,GAAsBU,MAAO,CAAAX,WAAW,IAAI,EAAK,GAAAW,MAAA,CAAOZ,IAAI,CAAI,GAAAC,WAAA;IAC7EE,SAAA,EAAW,CAAC,UAAU,UAAUd,WAAA,CAAYM,QAAQ,CAAK,IAAAQ,SAAS,CAAE,CAAAU,IAAA,CAAK,GAAG;IAC5E,GAAGR;EACL,GACA,CACE,GAAGT,QAAS,CAAAkB,GAAA,CAAI,CAAC,CAACC,GAAK,EAAAC,KAAK,CAAM,KAAAT,aAAA,CAAcQ,GAAK,EAAAC,KAAK,CAAC,GAC3D,IAAIC,KAAM,CAAAC,OAAA,CAAQd,QAAQ,CAAI,GAAAA,QAAA,GAAW,CAACA,QAAQ,GAEtD,EACJ;EAEAP,SAAA,CAAUsB,WAAA,GAAc,GAAGxB,QAAA;EAEpB,OAAAE,SAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}