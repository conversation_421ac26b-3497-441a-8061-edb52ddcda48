@echo off
echo ========================================
echo    PRESSURE MAX - QUICK DEV START
echo ========================================

:: Set working directory to script location
cd /d "%~dp0"

:: Start API server
echo 🔧 Starting API Server...
start "API Server" cmd /k "cd pressure-max-api && npm run dev"

:: Wait 2 seconds
timeout /t 2 /nobreak >nul

:: Start Frontend
echo 🎨 Starting Frontend...
start "Frontend" cmd /k "cd pressure-max-frontend && npm start"

echo.
echo ✅ Services starting...
echo 📱 Frontend: http://localhost:3001
echo 🔧 API: http://localhost:3000
echo.
echo Press any key to close this window...
pause >nul
