{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\components\\\\CampaignSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { facebookAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\nimport { Target, Plus, DollarSign, Calendar, AlertCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CAMPAIGN_OBJECTIVES = [{\n  value: 'REACH',\n  label: 'Reach'\n}, {\n  value: 'TRAFFIC',\n  label: 'Traffic'\n}, {\n  value: 'ENGAGEMENT',\n  label: 'Engagement'\n}, {\n  value: 'APP_INSTALLS',\n  label: 'App Installs'\n}, {\n  value: 'VIDEO_VIEWS',\n  label: 'Video Views'\n}, {\n  value: 'LEAD_GENERATION',\n  label: 'Lead Generation'\n}, {\n  value: 'MESSAGES',\n  label: 'Messages'\n}, {\n  value: 'CONVERSIONS',\n  label: 'Conversions'\n}, {\n  value: 'CATALOG_SALES',\n  label: 'Catalog Sales'\n}, {\n  value: 'STORE_VISITS',\n  label: 'Store Visits'\n}];\nconst CampaignSection = () => {\n  _s();\n  const {\n    isAuthenticated\n  } = useAuth();\n  const [campaigns, setCampaigns] = useState([]);\n  const [adSets, setAdSets] = useState([]);\n  const [ads, setAds] = useState([]);\n  const [adAccounts, setAdAccounts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [selectedAccount, setSelectedAccount] = useState('');\n  const [activeTab, setActiveTab] = useState('campaigns');\n  const {\n    register,\n    handleSubmit,\n    reset,\n    formState: {\n      errors\n    }\n  } = useForm();\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadAdAccounts();\n    }\n  }, [isAuthenticated]);\n  useEffect(() => {\n    if (selectedAccount) {\n      loadCampaigns();\n      loadAdSets();\n      loadAds();\n    }\n  }, [selectedAccount]);\n  const loadAdAccounts = async () => {\n    try {\n      var _response$data;\n      const response = await facebookAPI.getAdAccounts();\n      setAdAccounts(response.data || []);\n      if (((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.length) > 0) {\n        setSelectedAccount(response.data[0].account_id);\n      }\n    } catch (error) {\n      toast.error('Failed to load ad accounts');\n    }\n  };\n  const loadCampaigns = async () => {\n    if (!selectedAccount) return;\n    try {\n      setLoading(true);\n      const response = await facebookAPI.getCampaigns(selectedAccount);\n      setCampaigns(response.data || []);\n    } catch (error) {\n      toast.error('Failed to load campaigns');\n      setCampaigns([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadAdSets = async () => {\n    if (!selectedAccount) return;\n    try {\n      const response = await api.get(`/facebook/adsets/${selectedAccount}`);\n      setAdSets(response.data || []);\n    } catch (error) {\n      console.error('Failed to load ad sets:', error);\n      toast.error('Failed to load ad sets');\n      setAdSets([]);\n    }\n  };\n  const loadAds = async () => {\n    if (!selectedAccount) return;\n    try {\n      const response = await api.get(`/facebook/ads/${selectedAccount}`);\n      setAds(response.data || []);\n    } catch (error) {\n      console.error('Failed to load ads:', error);\n      toast.error('Failed to load ads');\n      setAds([]);\n    }\n  };\n  const onCreateCampaign = async data => {\n    try {\n      setLoading(true);\n      const campaignData = {\n        name: data.name,\n        objective: data.objective,\n        status: 'PAUSED',\n        // Always start paused for safety\n        specialAdCategories: data.specialAdCategories ? [data.specialAdCategories] : []\n      };\n      await facebookAPI.createCampaign(selectedAccount, campaignData);\n      toast.success('Campaign created successfully!');\n\n      // Reset form and reload campaigns\n      reset();\n      setShowCreateForm(false);\n      loadCampaigns();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to create campaign');\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"campaign-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Campaign Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-required\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please log in to manage campaigns\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this);\n  }\n  if (adAccounts.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"campaign-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Campaign Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-accounts\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No Facebook ad accounts found. Please connect your Facebook account first.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"campaign-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Campaign Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"account-selector\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"Select Ad Account:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: selectedAccount,\n        onChange: e => setSelectedAccount(e.target.value),\n        children: adAccounts.map(account => /*#__PURE__*/_jsxDEV(\"option\", {\n          value: account.account_id,\n          children: [account.name, \" (\", account.account_id, \")\"]\n        }, account.account_id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"campaigns-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: [/*#__PURE__*/_jsxDEV(Target, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), \"Campaigns (\", campaigns.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowCreateForm(!showCreateForm),\n        className: \"create-btn\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), showCreateForm ? 'Cancel' : 'Create Campaign']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), showCreateForm && /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit(onCreateCampaign),\n      className: \"create-campaign-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Create New Campaign\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Campaign Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...register('name', {\n            required: 'Campaign name is required'\n          }),\n          placeholder: \"Enter campaign name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this), errors.name && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: errors.name.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Objective:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          ...register('objective', {\n            required: 'Objective is required'\n          }),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select objective\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), CAMPAIGN_OBJECTIVES.map(obj => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: obj.value,\n            children: obj.label\n          }, obj.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this), errors.objective && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: errors.objective.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 34\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Special Ad Categories (optional):\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          ...register('specialAdCategories'),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"None\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"CREDIT\",\n            children: \"Credit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"EMPLOYMENT\",\n            children: \"Employment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"HOUSING\",\n            children: \"Housing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"ISSUES_ELECTIONS_POLITICS\",\n            children: \"Issues, Elections or Politics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading,\n          className: \"submit-btn\",\n          children: loading ? 'Creating...' : 'Create Campaign'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setShowCreateForm(false),\n          className: \"cancel-btn\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"campaigns-list\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading campaigns...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 11\n      }, this) : campaigns.length > 0 ? campaigns.map(campaign => {\n        var _campaign$status;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"campaign-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"campaign-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: campaign.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `campaign-status ${(_campaign$status = campaign.status) === null || _campaign$status === void 0 ? void 0 : _campaign$status.toLowerCase()}`,\n              children: campaign.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"campaign-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(Target, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Objective: \", campaign.objective]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Created: \", new Date(campaign.created_time).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), campaign.daily_budget && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Daily Budget: $\", (campaign.daily_budget / 100).toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)]\n        }, campaign.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this);\n      }) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-campaigns\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No campaigns found for this ad account.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Create your first campaign using the form above.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(CampaignSection, \"vLpNIOBYQwwMXKcL0jaY1CygeIc=\", false, function () {\n  return [useAuth, useForm];\n});\n_c = CampaignSection;\nexport default CampaignSection;\nvar _c;\n$RefreshReg$(_c, \"CampaignSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "facebookAPI", "useAuth", "useForm", "toast", "Target", "Plus", "DollarSign", "Calendar", "AlertCircle", "jsxDEV", "_jsxDEV", "CAMPAIGN_OBJECTIVES", "value", "label", "CampaignSection", "_s", "isAuthenticated", "campaigns", "setCampaigns", "adSets", "setAdSets", "ads", "setAds", "adAccounts", "setAdAccounts", "loading", "setLoading", "showCreateForm", "setShowCreateForm", "selectedAccount", "setSelectedAccount", "activeTab", "setActiveTab", "register", "handleSubmit", "reset", "formState", "errors", "loadAdAccounts", "loadCampaigns", "loadAdSets", "loadAds", "_response$data", "response", "getAdAccounts", "data", "length", "account_id", "error", "getCampaigns", "api", "get", "console", "onCreateCampaign", "campaignData", "name", "objective", "status", "specialAdCategories", "createCampaign", "success", "_error$response", "_error$response$data", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onChange", "e", "target", "map", "account", "onClick", "onSubmit", "type", "required", "placeholder", "obj", "disabled", "campaign", "_campaign$status", "toLowerCase", "Date", "created_time", "toLocaleDateString", "daily_budget", "toFixed", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/components/CampaignSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { facebookAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\nimport { Target, Plus, DollarSign, Calendar, AlertCircle } from 'lucide-react';\n\nconst CAMPAIGN_OBJECTIVES = [\n  { value: 'REACH', label: 'Reach' },\n  { value: 'TRAFFIC', label: 'Traffic' },\n  { value: 'ENGAGEMENT', label: 'Engagement' },\n  { value: 'APP_INSTALLS', label: 'App Installs' },\n  { value: 'VIDEO_VIEWS', label: 'Video Views' },\n  { value: 'LEAD_GENERATION', label: 'Lead Generation' },\n  { value: 'MESSAGES', label: 'Messages' },\n  { value: 'CONVERSIONS', label: 'Conversions' },\n  { value: 'CATALOG_SALES', label: 'Catalog Sales' },\n  { value: 'STORE_VISITS', label: 'Store Visits' }\n];\n\nconst CampaignSection = () => {\n  const { isAuthenticated } = useAuth();\n  const [campaigns, setCampaigns] = useState([]);\n  const [adSets, setAdSets] = useState([]);\n  const [ads, setAds] = useState([]);\n  const [adAccounts, setAdAccounts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [selectedAccount, setSelectedAccount] = useState('');\n  const [activeTab, setActiveTab] = useState('campaigns');\n\n  const { register, handleSubmit, reset, formState: { errors } } = useForm();\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadAdAccounts();\n    }\n  }, [isAuthenticated]);\n\n  useEffect(() => {\n    if (selectedAccount) {\n      loadCampaigns();\n      loadAdSets();\n      loadAds();\n    }\n  }, [selectedAccount]);\n\n  const loadAdAccounts = async () => {\n    try {\n      const response = await facebookAPI.getAdAccounts();\n      setAdAccounts(response.data || []);\n      if (response.data?.length > 0) {\n        setSelectedAccount(response.data[0].account_id);\n      }\n    } catch (error) {\n      toast.error('Failed to load ad accounts');\n    }\n  };\n\n  const loadCampaigns = async () => {\n    if (!selectedAccount) return;\n    \n    try {\n      setLoading(true);\n      const response = await facebookAPI.getCampaigns(selectedAccount);\n      setCampaigns(response.data || []);\n    } catch (error) {\n      toast.error('Failed to load campaigns');\n      setCampaigns([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadAdSets = async () => {\n    if (!selectedAccount) return;\n\n    try {\n      const response = await api.get(`/facebook/adsets/${selectedAccount}`);\n      setAdSets(response.data || []);\n    } catch (error) {\n      console.error('Failed to load ad sets:', error);\n      toast.error('Failed to load ad sets');\n      setAdSets([]);\n    }\n  };\n\n  const loadAds = async () => {\n    if (!selectedAccount) return;\n\n    try {\n      const response = await api.get(`/facebook/ads/${selectedAccount}`);\n      setAds(response.data || []);\n    } catch (error) {\n      console.error('Failed to load ads:', error);\n      toast.error('Failed to load ads');\n      setAds([]);\n    }\n  };\n\n  const onCreateCampaign = async (data) => {\n    try {\n      setLoading(true);\n      const campaignData = {\n        name: data.name,\n        objective: data.objective,\n        status: 'PAUSED', // Always start paused for safety\n        specialAdCategories: data.specialAdCategories ? [data.specialAdCategories] : []\n      };\n\n      await facebookAPI.createCampaign(selectedAccount, campaignData);\n      toast.success('Campaign created successfully!');\n      \n      // Reset form and reload campaigns\n      reset();\n      setShowCreateForm(false);\n      loadCampaigns();\n    } catch (error) {\n      toast.error(error.response?.data?.message || 'Failed to create campaign');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"campaign-section\">\n        <h2>Campaign Management</h2>\n        <div className=\"auth-required\">\n          <AlertCircle size={20} />\n          <p>Please log in to manage campaigns</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (adAccounts.length === 0) {\n    return (\n      <div className=\"campaign-section\">\n        <h2>Campaign Management</h2>\n        <div className=\"no-accounts\">\n          <AlertCircle size={20} />\n          <p>No Facebook ad accounts found. Please connect your Facebook account first.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"campaign-section\">\n      <h2>Campaign Management</h2>\n      \n      <div className=\"account-selector\">\n        <label>Select Ad Account:</label>\n        <select \n          value={selectedAccount} \n          onChange={(e) => setSelectedAccount(e.target.value)}\n        >\n          {adAccounts.map((account) => (\n            <option key={account.account_id} value={account.account_id}>\n              {account.name} ({account.account_id})\n            </option>\n          ))}\n        </select>\n      </div>\n\n      <div className=\"campaigns-header\">\n        <h3>\n          <Target size={16} />\n          Campaigns ({campaigns.length})\n        </h3>\n        <button \n          onClick={() => setShowCreateForm(!showCreateForm)}\n          className=\"create-btn\"\n        >\n          <Plus size={16} />\n          {showCreateForm ? 'Cancel' : 'Create Campaign'}\n        </button>\n      </div>\n\n      {showCreateForm && (\n        <form onSubmit={handleSubmit(onCreateCampaign)} className=\"create-campaign-form\">\n          <h4>Create New Campaign</h4>\n          \n          <div className=\"form-group\">\n            <label>Campaign Name:</label>\n            <input\n              type=\"text\"\n              {...register('name', { required: 'Campaign name is required' })}\n              placeholder=\"Enter campaign name\"\n            />\n            {errors.name && <span className=\"error\">{errors.name.message}</span>}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Objective:</label>\n            <select {...register('objective', { required: 'Objective is required' })}>\n              <option value=\"\">Select objective</option>\n              {CAMPAIGN_OBJECTIVES.map((obj) => (\n                <option key={obj.value} value={obj.value}>\n                  {obj.label}\n                </option>\n              ))}\n            </select>\n            {errors.objective && <span className=\"error\">{errors.objective.message}</span>}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Special Ad Categories (optional):</label>\n            <select {...register('specialAdCategories')}>\n              <option value=\"\">None</option>\n              <option value=\"CREDIT\">Credit</option>\n              <option value=\"EMPLOYMENT\">Employment</option>\n              <option value=\"HOUSING\">Housing</option>\n              <option value=\"ISSUES_ELECTIONS_POLITICS\">Issues, Elections or Politics</option>\n            </select>\n          </div>\n\n          <div className=\"form-actions\">\n            <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n              {loading ? 'Creating...' : 'Create Campaign'}\n            </button>\n            <button \n              type=\"button\" \n              onClick={() => setShowCreateForm(false)}\n              className=\"cancel-btn\"\n            >\n              Cancel\n            </button>\n          </div>\n        </form>\n      )}\n\n      <div className=\"campaigns-list\">\n        {loading ? (\n          <div className=\"loading\">Loading campaigns...</div>\n        ) : campaigns.length > 0 ? (\n          campaigns.map((campaign) => (\n            <div key={campaign.id} className=\"campaign-item\">\n              <div className=\"campaign-header\">\n                <h4>{campaign.name}</h4>\n                <span className={`campaign-status ${campaign.status?.toLowerCase()}`}>\n                  {campaign.status}\n                </span>\n              </div>\n              <div className=\"campaign-details\">\n                <div className=\"detail-item\">\n                  <Target size={14} />\n                  <span>Objective: {campaign.objective}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <Calendar size={14} />\n                  <span>Created: {new Date(campaign.created_time).toLocaleDateString()}</span>\n                </div>\n                {campaign.daily_budget && (\n                  <div className=\"detail-item\">\n                    <DollarSign size={14} />\n                    <span>Daily Budget: ${(campaign.daily_budget / 100).toFixed(2)}</span>\n                  </div>\n                )}\n              </div>\n            </div>\n          ))\n        ) : (\n          <div className=\"no-campaigns\">\n            <p>No campaigns found for this ad account.</p>\n            <p>Create your first campaign using the form above.</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CampaignSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,MAAMC,mBAAmB,GAAG,CAC1B;EAAEC,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAClC;EAAED,KAAK,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAU,CAAC,EACtC;EAAED,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAa,CAAC,EAC5C;EAAED,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAe,CAAC,EAChD;EAAED,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAc,CAAC,EAC9C;EAAED,KAAK,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAAkB,CAAC,EACtD;EAAED,KAAK,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAW,CAAC,EACxC;EAAED,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAc,CAAC,EAC9C;EAAED,KAAK,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAgB,CAAC,EAClD;EAAED,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAe,CAAC,CACjD;AAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC;EAAgB,CAAC,GAAGf,OAAO,CAAC,CAAC;EACrC,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuB,GAAG,EAAEC,MAAM,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,WAAW,CAAC;EAEvD,MAAM;IAAEmC,QAAQ;IAAEC,YAAY;IAAEC,KAAK;IAAEC,SAAS,EAAE;MAAEC;IAAO;EAAE,CAAC,GAAGnC,OAAO,CAAC,CAAC;EAE1EH,SAAS,CAAC,MAAM;IACd,IAAIiB,eAAe,EAAE;MACnBsB,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACtB,eAAe,CAAC,CAAC;EAErBjB,SAAS,CAAC,MAAM;IACd,IAAI8B,eAAe,EAAE;MACnBU,aAAa,CAAC,CAAC;MACfC,UAAU,CAAC,CAAC;MACZC,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE,CAACZ,eAAe,CAAC,CAAC;EAErB,MAAMS,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MAAA,IAAAI,cAAA;MACF,MAAMC,QAAQ,GAAG,MAAM3C,WAAW,CAAC4C,aAAa,CAAC,CAAC;MAClDpB,aAAa,CAACmB,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;MAClC,IAAI,EAAAH,cAAA,GAAAC,QAAQ,CAACE,IAAI,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,MAAM,IAAG,CAAC,EAAE;QAC7BhB,kBAAkB,CAACa,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAACE,UAAU,CAAC;MACjD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd7C,KAAK,CAAC6C,KAAK,CAAC,4BAA4B,CAAC;IAC3C;EACF,CAAC;EAED,MAAMT,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACV,eAAe,EAAE;IAEtB,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiB,QAAQ,GAAG,MAAM3C,WAAW,CAACiD,YAAY,CAACpB,eAAe,CAAC;MAChEX,YAAY,CAACyB,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd7C,KAAK,CAAC6C,KAAK,CAAC,0BAA0B,CAAC;MACvC9B,YAAY,CAAC,EAAE,CAAC;IAClB,CAAC,SAAS;MACRQ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACX,eAAe,EAAE;IAEtB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMO,GAAG,CAACC,GAAG,CAAC,oBAAoBtB,eAAe,EAAE,CAAC;MACrET,SAAS,CAACuB,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IAChC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C7C,KAAK,CAAC6C,KAAK,CAAC,wBAAwB,CAAC;MACrC5B,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;EAED,MAAMqB,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI,CAACZ,eAAe,EAAE;IAEtB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMO,GAAG,CAACC,GAAG,CAAC,iBAAiBtB,eAAe,EAAE,CAAC;MAClEP,MAAM,CAACqB,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IAC7B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C7C,KAAK,CAAC6C,KAAK,CAAC,oBAAoB,CAAC;MACjC1B,MAAM,CAAC,EAAE,CAAC;IACZ;EACF,CAAC;EAED,MAAM+B,gBAAgB,GAAG,MAAOR,IAAI,IAAK;IACvC,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM4B,YAAY,GAAG;QACnBC,IAAI,EAAEV,IAAI,CAACU,IAAI;QACfC,SAAS,EAAEX,IAAI,CAACW,SAAS;QACzBC,MAAM,EAAE,QAAQ;QAAE;QAClBC,mBAAmB,EAAEb,IAAI,CAACa,mBAAmB,GAAG,CAACb,IAAI,CAACa,mBAAmB,CAAC,GAAG;MAC/E,CAAC;MAED,MAAM1D,WAAW,CAAC2D,cAAc,CAAC9B,eAAe,EAAEyB,YAAY,CAAC;MAC/DnD,KAAK,CAACyD,OAAO,CAAC,gCAAgC,CAAC;;MAE/C;MACAzB,KAAK,CAAC,CAAC;MACPP,iBAAiB,CAAC,KAAK,CAAC;MACxBW,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOS,KAAK,EAAE;MAAA,IAAAa,eAAA,EAAAC,oBAAA;MACd3D,KAAK,CAAC6C,KAAK,CAAC,EAAAa,eAAA,GAAAb,KAAK,CAACL,QAAQ,cAAAkB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBhB,IAAI,cAAAiB,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,2BAA2B,CAAC;IAC3E,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAI,CAACV,eAAe,EAAE;IACpB,oBACEN,OAAA;MAAKsD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BvD,OAAA;QAAAuD,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5B3D,OAAA;QAAKsD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BvD,OAAA,CAACF,WAAW;UAAC8D,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzB3D,OAAA;UAAAuD,QAAA,EAAG;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI9C,UAAU,CAACuB,MAAM,KAAK,CAAC,EAAE;IAC3B,oBACEpC,OAAA;MAAKsD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BvD,OAAA;QAAAuD,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5B3D,OAAA;QAAKsD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvD,OAAA,CAACF,WAAW;UAAC8D,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzB3D,OAAA;UAAAuD,QAAA,EAAG;QAA0E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE3D,OAAA;IAAKsD,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BvD,OAAA;MAAAuD,QAAA,EAAI;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE5B3D,OAAA;MAAKsD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BvD,OAAA;QAAAuD,QAAA,EAAO;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACjC3D,OAAA;QACEE,KAAK,EAAEiB,eAAgB;QACvB0C,QAAQ,EAAGC,CAAC,IAAK1C,kBAAkB,CAAC0C,CAAC,CAACC,MAAM,CAAC7D,KAAK,CAAE;QAAAqD,QAAA,EAEnD1C,UAAU,CAACmD,GAAG,CAAEC,OAAO,iBACtBjE,OAAA;UAAiCE,KAAK,EAAE+D,OAAO,CAAC5B,UAAW;UAAAkB,QAAA,GACxDU,OAAO,CAACpB,IAAI,EAAC,IAAE,EAACoB,OAAO,CAAC5B,UAAU,EAAC,GACtC;QAAA,GAFa4B,OAAO,CAAC5B,UAAU;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEvB,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN3D,OAAA;MAAKsD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BvD,OAAA;QAAAuD,QAAA,gBACEvD,OAAA,CAACN,MAAM;UAACkE,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACT,EAACpD,SAAS,CAAC6B,MAAM,EAAC,GAC/B;MAAA;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL3D,OAAA;QACEkE,OAAO,EAAEA,CAAA,KAAMhD,iBAAiB,CAAC,CAACD,cAAc,CAAE;QAClDqC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEtBvD,OAAA,CAACL,IAAI;UAACiE,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACjB1C,cAAc,GAAG,QAAQ,GAAG,iBAAiB;MAAA;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL1C,cAAc,iBACbjB,OAAA;MAAMmE,QAAQ,EAAE3C,YAAY,CAACmB,gBAAgB,CAAE;MAACW,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAC9EvD,OAAA;QAAAuD,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE5B3D,OAAA;QAAKsD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBvD,OAAA;UAAAuD,QAAA,EAAO;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7B3D,OAAA;UACEoE,IAAI,EAAC,MAAM;UAAA,GACP7C,QAAQ,CAAC,MAAM,EAAE;YAAE8C,QAAQ,EAAE;UAA4B,CAAC,CAAC;UAC/DC,WAAW,EAAC;QAAqB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACDhC,MAAM,CAACkB,IAAI,iBAAI7C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE5B,MAAM,CAACkB,IAAI,CAACQ;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eAEN3D,OAAA;QAAKsD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBvD,OAAA;UAAAuD,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzB3D,OAAA;UAAA,GAAYuB,QAAQ,CAAC,WAAW,EAAE;YAAE8C,QAAQ,EAAE;UAAwB,CAAC,CAAC;UAAAd,QAAA,gBACtEvD,OAAA;YAAQE,KAAK,EAAC,EAAE;YAAAqD,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACzC1D,mBAAmB,CAAC+D,GAAG,CAAEO,GAAG,iBAC3BvE,OAAA;YAAwBE,KAAK,EAAEqE,GAAG,CAACrE,KAAM;YAAAqD,QAAA,EACtCgB,GAAG,CAACpE;UAAK,GADCoE,GAAG,CAACrE,KAAK;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEd,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EACRhC,MAAM,CAACmB,SAAS,iBAAI9C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE5B,MAAM,CAACmB,SAAS,CAACO;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAEN3D,OAAA;QAAKsD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBvD,OAAA;UAAAuD,QAAA,EAAO;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChD3D,OAAA;UAAA,GAAYuB,QAAQ,CAAC,qBAAqB,CAAC;UAAAgC,QAAA,gBACzCvD,OAAA;YAAQE,KAAK,EAAC,EAAE;YAAAqD,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9B3D,OAAA;YAAQE,KAAK,EAAC,QAAQ;YAAAqD,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC3D,OAAA;YAAQE,KAAK,EAAC,YAAY;YAAAqD,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9C3D,OAAA;YAAQE,KAAK,EAAC,SAAS;YAAAqD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxC3D,OAAA;YAAQE,KAAK,EAAC,2BAA2B;YAAAqD,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN3D,OAAA;QAAKsD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BvD,OAAA;UAAQoE,IAAI,EAAC,QAAQ;UAACI,QAAQ,EAAEzD,OAAQ;UAACuC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAC5DxC,OAAO,GAAG,aAAa,GAAG;QAAiB;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACT3D,OAAA;UACEoE,IAAI,EAAC,QAAQ;UACbF,OAAO,EAAEA,CAAA,KAAMhD,iBAAiB,CAAC,KAAK,CAAE;UACxCoC,SAAS,EAAC,YAAY;UAAAC,QAAA,EACvB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP,eAED3D,OAAA;MAAKsD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC5BxC,OAAO,gBACNf,OAAA;QAAKsD,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,GACjDpD,SAAS,CAAC6B,MAAM,GAAG,CAAC,GACtB7B,SAAS,CAACyD,GAAG,CAAES,QAAQ;QAAA,IAAAC,gBAAA;QAAA,oBACrB1E,OAAA;UAAuBsD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC9CvD,OAAA;YAAKsD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BvD,OAAA;cAAAuD,QAAA,EAAKkB,QAAQ,CAAC5B;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxB3D,OAAA;cAAMsD,SAAS,EAAE,oBAAAoB,gBAAA,GAAmBD,QAAQ,CAAC1B,MAAM,cAAA2B,gBAAA,uBAAfA,gBAAA,CAAiBC,WAAW,CAAC,CAAC,EAAG;cAAApB,QAAA,EAClEkB,QAAQ,CAAC1B;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BvD,OAAA;cAAKsD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BvD,OAAA,CAACN,MAAM;gBAACkE,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpB3D,OAAA;gBAAAuD,QAAA,GAAM,aAAW,EAACkB,QAAQ,CAAC3B,SAAS;cAAA;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACN3D,OAAA;cAAKsD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BvD,OAAA,CAACH,QAAQ;gBAAC+D,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB3D,OAAA;gBAAAuD,QAAA,GAAM,WAAS,EAAC,IAAIqB,IAAI,CAACH,QAAQ,CAACI,YAAY,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,EACLc,QAAQ,CAACM,YAAY,iBACpB/E,OAAA;cAAKsD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BvD,OAAA,CAACJ,UAAU;gBAACgE,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxB3D,OAAA;gBAAAuD,QAAA,GAAM,iBAAe,EAAC,CAACkB,QAAQ,CAACM,YAAY,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GAtBEc,QAAQ,CAACQ,EAAE;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBhB,CAAC;MAAA,CACP,CAAC,gBAEF3D,OAAA;QAAKsD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BvD,OAAA;UAAAuD,QAAA,EAAG;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9C3D,OAAA;UAAAuD,QAAA,EAAG;QAAgD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CA5PID,eAAe;EAAA,QACSb,OAAO,EAU8BC,OAAO;AAAA;AAAA0F,EAAA,GAXpE9E,eAAe;AA8PrB,eAAeA,eAAe;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}