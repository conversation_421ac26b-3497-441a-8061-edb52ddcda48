const express = require('express');
const cors = require('cors');

const app = express();

// Rate limiting and caching setup
const cache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache
const API_DELAY = 1000; // 1 second delay between API calls
let lastApiCall = 0;

// Rate limiting helper
async function rateLimitedApiCall(apiCallFunction) {
  const now = Date.now();
  const timeSinceLastCall = now - lastApiCall;

  if (timeSinceLastCall < API_DELAY) {
    const waitTime = API_DELAY - timeSinceLastCall;
    console.log(`Rate limiting: waiting ${waitTime}ms before next API call`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }

  lastApiCall = Date.now();
  return await apiCallFunction();
}

// Cache helper functions
function getCacheKey(endpoint, params) {
  return `${endpoint}_${JSON.stringify(params)}`;
}

function getFromCache(key) {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log(`Cache hit for ${key}`);
    return cached.data;
  }
  return null;
}

function setCache(key, data) {
  cache.set(key, {
    data: data,
    timestamp: Date.now()
  });
  console.log(`Cached data for ${key}`);
}
const port = 3000;

// Helper function to ensure ad account ID has proper format
function ensureActPrefix(adAccountId) {
  if (!adAccountId) return adAccountId;
  // If it already starts with 'act_', return as is
  if (adAccountId.startsWith('act_')) return adAccountId;
  // If it's just the numeric ID, add 'act_' prefix
  return `act_${adAccountId}`;
}

// Basic middleware
app.use(cors({
  origin: 'http://localhost:3001',
  credentials: true
}));
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0'
  });
});

// Basic auth endpoints
app.post('/api/v1/auth/register', (req, res) => {
  console.log('Register request:', req.body);
  res.status(201).json({
    message: 'User registered successfully',
    user: {
      id: 'user_123',
      email: req.body.email,
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      role: 'user'
    },
    tokens: {
      accessToken: 'demo_access_token_123',
      refreshToken: 'demo_refresh_token_123'
    }
  });
});

app.post('/api/v1/auth/login', (req, res) => {
  console.log('Login request:', req.body);
  res.json({
    message: 'Login successful',
    user: {
      id: 'user_123',
      email: req.body.email,
      firstName: 'Demo',
      lastName: 'User',
      role: 'user'
    },
    tokens: {
      accessToken: 'demo_access_token_123',
      refreshToken: 'demo_refresh_token_123'
    }
  });
});

app.post('/api/v1/auth/logout', (req, res) => {
  res.json({ message: 'Logout successful' });
});

// User profile endpoint
app.get('/api/v1/users/profile', async (req, res) => {
  try {
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    // Get real Facebook user profile
    const axios = require('axios');
    const response = await axios.get('https://graph.facebook.com/v23.0/me', {
      params: {
        fields: 'id,name,email,first_name,last_name,picture',
        access_token: accessToken
      }
    });

    const fbUser = response.data;
    console.log('Facebook User Profile:', fbUser);

    res.json({
      id: `fb_${fbUser.id}`,
      email: fbUser.email || '<EMAIL>',
      firstName: fbUser.first_name || 'Facebook',
      lastName: fbUser.last_name || 'User',
      name: fbUser.name,
      role: 'user',
      isActive: true,
      lastLoginAt: new Date().toISOString(),
      picture: fbUser.picture?.data?.url,
      facebookId: fbUser.id
    });
  } catch (error) {
    console.error('Facebook user profile error:', error.response?.data || error.message);

    // Fallback to demo data if API fails
    res.json({
      id: 'user_fb_demo',
      email: '<EMAIL>',
      firstName: 'Facebook',
      lastName: 'User',
      role: 'user',
      isActive: true,
      lastLoginAt: new Date().toISOString(),
      error: 'Real API call failed, showing demo data'
    });
  }
});

// Facebook endpoints
app.get('/api/v1/facebook/oauth-url', (req, res) => {
  const { redirectUri } = req.query;
  const state = 'demo_state_' + Date.now();

  // Real Facebook OAuth URL with actual app ID
  const permissions = [
    'ads_management',
    'ads_read',
    'business_management',
    'pages_read_engagement',
    'pages_manage_ads',
    'leads_retrieval',
    'email',
    'public_profile'
  ].join(',');

  const oauthUrl = `https://www.facebook.com/v18.0/dialog/oauth?` +
    `client_id=394349039883481&` +
    `redirect_uri=${encodeURIComponent(redirectUri || 'http://localhost:3001/facebook-callback')}&` +
    `scope=${encodeURIComponent(permissions)}&` +
    `response_type=code&` +
    `state=${state}`;

  console.log('Generated Facebook OAuth URL:', oauthUrl);

  res.json({
    oauthUrl,
    state
  });
});

// Facebook OAuth callback handler
app.post('/api/v1/facebook/oauth-callback', (req, res) => {
  const { code, state } = req.body;
  console.log('Facebook OAuth callback received:', { code: code?.substring(0, 20) + '...', state });

  // In a real implementation, you would:
  // 1. Exchange code for access token
  // 2. Get user profile from Facebook
  // 3. Create or update user in database
  // 4. Generate JWT tokens

  // For demo, return success with mock user data
  res.json({
    message: 'Facebook account connected successfully',
    user: {
      id: 'user_fb_' + Date.now(),
      email: '<EMAIL>',
      firstName: 'Facebook',
      lastName: 'User',
      role: 'user',
      facebookConnected: true
    },
    tokens: {
      accessToken: 'demo_fb_access_token_' + Date.now(),
      refreshToken: 'demo_fb_refresh_token_' + Date.now()
    },
    profile: {
      id: '*****************',
      name: 'Facebook User',
      email: '<EMAIL>'
    },
    permissions: [
      'ads_management',
      'ads_read',
      'business_management',
      'pages_read_engagement',
      'pages_manage_ads',
      'leads_retrieval',
      'email',
      'public_profile'
    ]
  });
});

app.get('/api/v1/facebook/ad-accounts', async (req, res) => {
  try {
    // Use the real Facebook access token
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    // Make real API call to Facebook
    const axios = require('axios');
    const response = await axios.get('https://graph.facebook.com/v23.0/me/adaccounts', {
      params: {
        fields: 'id,name,account_status,currency,timezone_name,business,account_id',
        access_token: accessToken
      }
    });

    console.log('Facebook Ad Accounts Response:', response.data);
    res.json(response.data.data || []);
  } catch (error) {
    console.error('Facebook API Error:', error.response?.data || error.message);

    // Fallback to demo data if API fails
    res.json([
      {
        id: 'act_demo',
        account_id: 'demo_account',
        name: 'Demo Ad Account (API Error)',
        account_status: 'ACTIVE',
        currency: 'USD',
        timezone_name: 'America/New_York',
        error: 'Real API call failed, showing demo data'
      }
    ]);
  }
});

app.get('/api/v1/facebook/pages', async (req, res) => {
  try {
    // Use the real Facebook access token
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    // Make real API call to Facebook
    const axios = require('axios');
    const response = await axios.get('https://graph.facebook.com/v23.0/me/accounts', {
      params: {
        fields: 'id,name,category,access_token,picture',
        access_token: accessToken
      }
    });

    console.log('Facebook Pages Response:', response.data);
    res.json(response.data.data || []);
  } catch (error) {
    console.error('Facebook Pages API Error:', error.response?.data || error.message);

    // Fallback to demo data if API fails
    res.json([
      {
        id: 'page_demo',
        name: 'Demo Business Page (API Error)',
        category: 'Business',
        error: 'Real API call failed, showing demo data'
      }
    ]);
  }
});

app.post('/api/v1/facebook/campaigns', async (req, res) => {
  try {
    const { adAccountId, name, objective, status = 'PAUSED' } = req.body;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    if (!adAccountId || !name || !objective) {
      return res.status(400).json({
        error: 'Missing required fields: adAccountId, name, objective'
      });
    }

    console.log('Creating Facebook campaign:', { adAccountId, name, objective, status });

    // Ensure ad account ID has proper format
    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('Using formatted ad account ID:', formattedAdAccountId);

    // Make real API call to Facebook to create campaign
    const axios = require('axios');
    const response = await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/campaigns`, null, {
      params: {
        name: name,
        objective: objective,
        status: status,
        special_ad_categories: '[]', // Required parameter for campaign creation
        access_token: accessToken
      }
    });

    console.log('Facebook campaign created successfully:', response.data);

    res.status(201).json({
      message: 'Campaign created successfully in Facebook',
      campaign: {
        id: response.data.id,
        name: name,
        objective: objective,
        status: status,
        created_time: new Date().toISOString(),
        facebook_id: response.data.id
      }
    });
  } catch (error) {
    console.error('Facebook campaign creation error:', error.response?.data || error.message);

    res.status(500).json({
      error: 'Failed to create campaign in Facebook',
      details: error.response?.data || error.message,
      fallback_campaign: {
        id: 'demo_' + Date.now(),
        name: req.body.name,
        objective: req.body.objective,
        status: 'DEMO_MODE',
        created_time: new Date().toISOString(),
        note: 'Campaign creation failed, this is demo data'
      }
    });
  }
});

app.get('/api/v1/facebook/campaigns/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('Fetching campaigns for ad account:', adAccountId);

    // Ensure ad account ID has proper format
    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('Using formatted ad account ID:', formattedAdAccountId);

    // Check cache first
    const cacheKey = getCacheKey('campaigns', { adAccountId: formattedAdAccountId });
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('Returning cached campaigns data');
      return res.json(cachedData);
    }

    // Make real API call to Facebook to get campaigns with enhanced fields
    const axios = require('axios');
    const response = await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/campaigns`, {
      params: {
        fields: 'id,name,objective,status,created_time,updated_time,daily_budget,lifetime_budget,budget_remaining,start_time,stop_time,bid_strategy,buying_type,can_create_brand_lift_study,can_use_spend_cap,configured_status,effective_status,issues_info,last_budget_toggling_time,pacing_type,promoted_object,recommendations,source_campaign,source_campaign_id,special_ad_categories,special_ad_category_country,spend_cap,topline_id',
        access_token: accessToken,
        limit: 50
      }
    });

    console.log('Facebook campaigns retrieved:', response.data);

    // Get performance insights for each campaign
    const campaignsWithInsights = await Promise.all(
      (response.data.data || []).map(async (campaign) => {
        try {
          const insightsResponse = await axios.get(`https://graph.facebook.com/v23.0/${campaign.id}/insights`, {
            params: {
              fields: 'impressions,clicks,spend,cpm,cpc,ctr,reach,frequency,actions,cost_per_action_type',
              access_token: accessToken,
              date_preset: 'last_30d'
            }
          });

          const insights = insightsResponse.data.data[0] || {};
          return {
            ...campaign,
            insights: {
              impressions: insights.impressions || '0',
              clicks: insights.clicks || '0',
              spend: insights.spend || '0.00',
              cpm: insights.cpm || '0.00',
              cpc: insights.cpc || '0.00',
              ctr: insights.ctr || '0.00',
              reach: insights.reach || '0',
              frequency: insights.frequency || '0.00',
              actions: insights.actions || [],
              cost_per_action_type: insights.cost_per_action_type || [],

            }
          };
        } catch (insightsError) {
          console.log(`No insights available for campaign ${campaign.id}:`, insightsError.response?.data?.error?.message || 'Unknown error');
          return {
            ...campaign,
            insights: {
              impressions: '0',
              clicks: '0',
              spend: '0.00',
              cpm: '0.00',
              cpc: '0.00',
              ctr: '0.00',
              reach: '0',
              frequency: '0.00',
              actions: [],
              cost_per_action_type: [],

            }
          };
        }
      })
    );

    res.json(campaignsWithInsights);
  } catch (error) {
    console.error('Facebook campaigns retrieval error:', error.response?.data || error.message);

    // Fallback to demo data if API fails
    res.json([
      {
        id: 'demo_camp_1',
        name: 'Demo Campaign (API Error)',
        objective: 'REACH',
        status: 'PAUSED',
        created_time: '2024-01-15T10:00:00Z',
        daily_budget: 5000,
        error: 'Real API call failed, showing demo data',
        error_details: error.response?.data || error.message
      }
    ]);
  }
});

// Ad Sets endpoint
app.get('/api/v1/facebook/adsets/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('Fetching ad sets for ad account:', adAccountId);

    // Ensure ad account ID has proper format
    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('Using formatted ad account ID:', formattedAdAccountId);

    // Make real API call to Facebook to get ad sets with enhanced fields
    const axios = require('axios');
    const response = await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/adsets`, {
      params: {
        fields: 'id,name,campaign_id,status,created_time,updated_time,daily_budget,lifetime_budget,budget_remaining,targeting,optimization_goal,billing_event,bid_amount,bid_strategy,configured_status,effective_status,end_time,frequency_control_specs,instagram_actor_id,is_dynamic_creative,issues_info,learning_stage_info,pacing_type,promoted_object,recommendations,recurring_budget_semantics,review_feedback,rf_prediction_id,source_adset,source_adset_id,start_time,targeting_optimization_types,time_based_ad_rotation_id_blocks,time_based_ad_rotation_intervals,use_new_app_click',
        access_token: accessToken,
        limit: 50
      }
    });

    console.log('Facebook ad sets retrieved:', response.data);

    // Get performance insights for each ad set
    const adSetsWithInsights = await Promise.all(
      (response.data.data || []).map(async (adSet) => {
        try {
          const insightsResponse = await axios.get(`https://graph.facebook.com/v23.0/${adSet.id}/insights`, {
            params: {
              fields: 'impressions,clicks,spend,cpm,cpc,ctr,reach,frequency,actions,cost_per_action_type,video_views,video_p25_watched_actions,video_p50_watched_actions,video_p75_watched_actions,video_p100_watched_actions,unique_clicks,unique_ctr,cost_per_unique_click',
              access_token: accessToken,
              date_preset: 'last_30d'
            }
          });

          const insights = insightsResponse.data.data[0] || {};
          return {
            ...adSet,
            insights: {
              impressions: insights.impressions || '0',
              clicks: insights.clicks || '0',
              spend: insights.spend || '0.00',
              cpm: insights.cpm || '0.00',
              cpc: insights.cpc || '0.00',
              ctr: insights.ctr || '0.00',
              reach: insights.reach || '0',
              frequency: insights.frequency || '0.00',
              actions: insights.actions || [],
              cost_per_action_type: insights.cost_per_action_type || [],
              video_views: insights.video_views || '0',
              video_p25_watched_actions: insights.video_p25_watched_actions || [],
              video_p50_watched_actions: insights.video_p50_watched_actions || [],
              video_p75_watched_actions: insights.video_p75_watched_actions || [],
              video_p100_watched_actions: insights.video_p100_watched_actions || [],
              unique_clicks: insights.unique_clicks || '0',
              unique_ctr: insights.unique_ctr || '0.00',
              cost_per_unique_click: insights.cost_per_unique_click || '0.00'
            }
          };
        } catch (insightsError) {
          console.log(`No insights available for ad set ${adSet.id}:`, insightsError.response?.data?.error?.message || 'Unknown error');
          return {
            ...adSet,
            insights: {
              impressions: '0',
              clicks: '0',
              spend: '0.00',
              cpm: '0.00',
              cpc: '0.00',
              ctr: '0.00',
              reach: '0',
              frequency: '0.00',
              actions: [],
              cost_per_action_type: [],
              video_views: '0',
              video_p25_watched_actions: [],
              video_p50_watched_actions: [],
              video_p75_watched_actions: [],
              video_p100_watched_actions: [],
              unique_clicks: '0',
              unique_ctr: '0.00',
              cost_per_unique_click: '0.00'
            }
          };
        }
      })
    );

    res.json(adSetsWithInsights);
  } catch (error) {
    console.error('Facebook ad sets retrieval error:', error.response?.data || error.message);

    // Fallback to demo data if API fails
    res.json([
      {
        id: 'demo_adset_1',
        name: 'Demo Ad Set (API Error)',
        campaign_id: 'demo_campaign_1',
        status: 'PAUSED',
        created_time: '2024-01-15T10:00:00Z',
        daily_budget: 2000,
        error: 'Real API call failed, showing demo data',
        error_details: error.response?.data || error.message
      }
    ]);
  }
});

// Ads endpoint
app.get('/api/v1/facebook/ads/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('Fetching ads for ad account:', adAccountId);

    // Ensure ad account ID has proper format
    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('Using formatted ad account ID:', formattedAdAccountId);

    // Make real API call to Facebook to get ads with enhanced fields
    const axios = require('axios');
    const response = await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/ads`, {
      params: {
        fields: 'id,name,adset_id,campaign_id,status,created_time,updated_time,creative{id,title,image_url,video_id,thumbnail_url,object_story_spec,call_to_action,link_url},bid_amount,configured_status,effective_status,preview_shareable_link',
        access_token: accessToken,
        limit: 50
      }
    });

    console.log('Facebook ads retrieved:', response.data);

    // Get performance insights for each ad
    const adsWithInsights = await Promise.all(
      (response.data.data || []).map(async (ad) => {
        try {
          const insightsResponse = await axios.get(`https://graph.facebook.com/v23.0/${ad.id}/insights`, {
            params: {
              fields: 'impressions,clicks,spend,cpm,cpc,ctr,reach,frequency,actions,cost_per_action_type,video_views,video_p25_watched_actions,video_p50_watched_actions,video_p75_watched_actions,video_p100_watched_actions,unique_clicks,unique_ctr,cost_per_unique_click,social_spend,inline_link_clicks,inline_link_click_ctr,cost_per_inline_link_click',
              access_token: accessToken,
              date_preset: 'last_30d'
            }
          });

          const insights = insightsResponse.data.data[0] || {};
          return {
            ...ad,
            insights: {
              impressions: insights.impressions || '0',
              clicks: insights.clicks || '0',
              spend: insights.spend || '0.00',
              cpm: insights.cpm || '0.00',
              cpc: insights.cpc || '0.00',
              ctr: insights.ctr || '0.00',
              reach: insights.reach || '0',
              frequency: insights.frequency || '0.00',
              actions: insights.actions || [],
              cost_per_action_type: insights.cost_per_action_type || [],
              video_views: insights.video_views || '0',
              video_p25_watched_actions: insights.video_p25_watched_actions || [],
              video_p50_watched_actions: insights.video_p50_watched_actions || [],
              video_p75_watched_actions: insights.video_p75_watched_actions || [],
              video_p100_watched_actions: insights.video_p100_watched_actions || [],
              unique_clicks: insights.unique_clicks || '0',
              unique_ctr: insights.unique_ctr || '0.00',
              cost_per_unique_click: insights.cost_per_unique_click || '0.00',
              social_spend: insights.social_spend || '0.00',
              inline_link_clicks: insights.inline_link_clicks || '0',
              inline_link_click_ctr: insights.inline_link_click_ctr || '0.00',
              cost_per_inline_link_click: insights.cost_per_inline_link_click || '0.00'
            }
          };
        } catch (insightsError) {
          console.log(`No insights available for ad ${ad.id}:`, insightsError.response?.data?.error?.message || 'Unknown error');
          return {
            ...ad,
            insights: {
              impressions: '0',
              clicks: '0',
              spend: '0.00',
              cpm: '0.00',
              cpc: '0.00',
              ctr: '0.00',
              reach: '0',
              frequency: '0.00',
              actions: [],
              cost_per_action_type: [],
              video_views: '0',
              video_p25_watched_actions: [],
              video_p50_watched_actions: [],
              video_p75_watched_actions: [],
              video_p100_watched_actions: [],
              unique_clicks: '0',
              unique_ctr: '0.00',
              cost_per_unique_click: '0.00',
              social_spend: '0.00',
              inline_link_clicks: '0',
              inline_link_click_ctr: '0.00',
              cost_per_inline_link_click: '0.00'
            }
          };
        }
      })
    );

    res.json(adsWithInsights);
  } catch (error) {
    console.error('Facebook ads retrieval error:', error.response?.data || error.message);

    // Fallback to demo data if API fails
    res.json([
      {
        id: 'demo_ad_1',
        name: 'Demo Ad (API Error)',
        adset_id: 'demo_adset_1',
        campaign_id: 'demo_campaign_1',
        status: 'PAUSED',
        created_time: '2024-01-15T10:00:00Z',
        error: 'Real API call failed, showing demo data',
        error_details: error.response?.data || error.message
      }
    ]);
  }
});

// Lead forms endpoint
app.get('/api/v1/facebook/leadforms/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('Fetching lead forms for ad account:', adAccountId);

    // Ensure ad account ID has proper format
    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('Using formatted ad account ID:', formattedAdAccountId);

    // Make real API call to Facebook to get lead forms
    const axios = require('axios');
    const response = await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/leadgen_forms`, {
      params: {
        fields: 'id,name,status,created_time,expired_leads_count,leads_count,locale,page,page_id,privacy_policy_url,questions,thank_you_page,context_card,follow_up_action_url,is_continued_flow,leadgen_export_csv_url,legal_content,organic_lead_retrieval_authorized,page_id,qualifiers,question_page_custom_headline,tracking_parameters',
        access_token: accessToken,
        limit: 50
      }
    });

    console.log('Facebook lead forms retrieved:', response.data);

    res.json(response.data.data || []);
  } catch (error) {
    console.error('Facebook lead forms retrieval error:', error.response?.data || error.message);

    // Fallback to empty array if API fails
    res.json([]);
  }
});

// Custom audiences endpoint
app.get('/api/v1/facebook/audiences/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('Fetching custom audiences for ad account:', adAccountId);

    // Ensure ad account ID has proper format
    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('Using formatted ad account ID:', formattedAdAccountId);

    // Make real API call to Facebook to get custom audiences
    const axios = require('axios');
    const response = await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/customaudiences`, {
      params: {
        fields: 'id,name,description,approximate_count,customer_file_source,data_source,delivery_status,external_event_source,is_value_based,lookalike_audience_ids,lookalike_spec,operation_status,opt_out_link,permission_for_actions,pixel_id,retention_days,rule,rule_aggregation,rule_v2,seed_audience,sharing_status,subtype,time_content_updated,time_created,time_updated',
        access_token: accessToken,
        limit: 50
      }
    });

    console.log('Facebook custom audiences retrieved:', response.data);

    res.json(response.data.data || []);
  } catch (error) {
    console.error('Facebook custom audiences retrieval error:', error.response?.data || error.message);

    // Fallback to empty array if API fails
    res.json([]);
  }
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `The requested endpoint ${req.originalUrl} does not exist`
  });
});

app.listen(port, () => {
  console.log(`🚀 Pressure Max API server running on port ${port}`);
  console.log(`📚 Health check available at http://localhost:${port}/health`);
  console.log(`🔗 CORS enabled for http://localhost:3001`);
});

module.exports = app;
