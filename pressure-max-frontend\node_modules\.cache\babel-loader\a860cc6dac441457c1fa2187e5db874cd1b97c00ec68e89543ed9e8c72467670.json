{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Reply = createLucideIcon(\"Reply\", [[\"polyline\", {\n  points: \"9 17 4 12 9 7\",\n  key: \"hvgpf2\"\n}], [\"path\", {\n  d: \"M20 18v-2a4 4 0 0 0-4-4H4\",\n  key: \"5vmcpk\"\n}]]);\nexport { Reply as default };", "map": {"version": 3, "names": ["Reply", "createLucideIcon", "points", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\node_modules\\lucide-react\\src\\icons\\reply.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Reply\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSI5IDE3IDQgMTIgOSA3IiAvPgogIDxwYXRoIGQ9Ik0yMCAxOHYtMmE0IDQgMCAwIDAtNC00SDQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/reply\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Reply = createLucideIcon('Reply', [\n  ['polyline', { points: '9 17 4 12 9 7', key: 'hvgpf2' }],\n  ['path', { d: 'M20 18v-2a4 4 0 0 0-4-4H4', key: '5vmcpk' }],\n]);\n\nexport default Reply;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,UAAY;EAAEC,MAAA,EAAQ,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,2BAA6B;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}