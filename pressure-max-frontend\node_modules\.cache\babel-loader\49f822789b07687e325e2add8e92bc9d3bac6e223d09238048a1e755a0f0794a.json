{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\components\\\\FacebookSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { facebookAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport toast from 'react-hot-toast';\nimport { Facebook, Users, CreditCard, AlertCircle, CheckCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FacebookSection = () => {\n  _s();\n  const {\n    isAuthenticated\n  } = useAuth();\n  const [facebookStatus, setFacebookStatus] = useState({\n    connected: false,\n    loading: false,\n    accounts: [],\n    pages: [],\n    error: null\n  });\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadFacebookData();\n    }\n  }, [isAuthenticated]);\n  const loadFacebookData = async () => {\n    try {\n      setFacebookStatus(prev => ({\n        ...prev,\n        loading: true,\n        error: null\n      }));\n      const [accountsResponse, pagesResponse] = await Promise.all([facebookAPI.getAdAccounts().catch(() => ({\n        data: []\n      })), facebookAPI.getPages().catch(() => ({\n        data: []\n      }))]);\n      setFacebookStatus(prev => ({\n        ...prev,\n        connected: accountsResponse.data.length > 0 || pagesResponse.data.length > 0,\n        accounts: accountsResponse.data || [],\n        pages: pagesResponse.data || [],\n        loading: false\n      }));\n    } catch (error) {\n      setFacebookStatus(prev => {\n        var _error$response, _error$response$data;\n        return {\n          ...prev,\n          loading: false,\n          error: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to load Facebook data'\n        };\n      });\n    }\n  };\n  const initiateOAuth = async () => {\n    try {\n      setFacebookStatus(prev => ({\n        ...prev,\n        loading: true\n      }));\n      const redirectUri = `${window.location.origin}/facebook-callback`;\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      if (response.data.oauthUrl) {\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL received');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      setFacebookStatus(prev => ({\n        ...prev,\n        loading: false\n      }));\n      toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to initiate Facebook OAuth');\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"facebook-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Facebook Integration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-required\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please log in to connect your Facebook account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"facebook-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Facebook Integration\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"facebook-status\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-header\",\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Connection Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), facebookStatus.connected ? /*#__PURE__*/_jsxDEV(CheckCircle, {\n          size: 16,\n          className: \"status-icon connected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 16,\n          className: \"status-icon disconnected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: `status-text ${facebookStatus.connected ? 'connected' : 'disconnected'}`,\n        children: facebookStatus.connected ? 'Connected to Facebook' : 'Not connected to Facebook'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), facebookStatus.error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: facebookStatus.error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), !facebookStatus.connected && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"oauth-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Connect your Facebook Business Manager account to start managing campaigns.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: initiateOAuth,\n        disabled: facebookStatus.loading,\n        className: \"oauth-btn\",\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this), facebookStatus.loading ? 'Connecting...' : 'Connect Facebook Account']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 9\n    }, this), facebookStatus.connected && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"facebook-data\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"data-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(CreditCard, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this), \"Ad Accounts (\", facebookStatus.accounts.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 13\n        }, this), facebookStatus.accounts.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"accounts-list\",\n          children: facebookStatus.accounts.map(account => {\n            // Convert numeric account status to readable string\n            const getAccountStatus = status => {\n              switch (status) {\n                case 1:\n                  return {\n                    text: 'ACTIVE',\n                    class: 'active'\n                  };\n                case 2:\n                  return {\n                    text: 'DISABLED',\n                    class: 'disabled'\n                  };\n                case 3:\n                  return {\n                    text: 'UNSETTLED',\n                    class: 'unsettled'\n                  };\n                case 7:\n                  return {\n                    text: 'PENDING_RISK_REVIEW',\n                    class: 'pending'\n                  };\n                case 8:\n                  return {\n                    text: 'PENDING_SETTLEMENT',\n                    class: 'pending'\n                  };\n                case 9:\n                  return {\n                    text: 'IN_GRACE_PERIOD',\n                    class: 'grace'\n                  };\n                case 100:\n                  return {\n                    text: 'PENDING_CLOSURE',\n                    class: 'pending'\n                  };\n                case 101:\n                  return {\n                    text: 'CLOSED',\n                    class: 'closed'\n                  };\n                case 201:\n                  return {\n                    text: 'ANY_ACTIVE',\n                    class: 'active'\n                  };\n                case 202:\n                  return {\n                    text: 'ANY_CLOSED',\n                    class: 'closed'\n                  };\n                default:\n                  return {\n                    text: `STATUS_${status}`,\n                    class: 'unknown'\n                  };\n              }\n            };\n            const statusInfo = getAccountStatus(account.account_status);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"account-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"account-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: account.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"account-id\",\n                  children: [\"ID: \", account.account_id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `account-status ${statusInfo.class}`,\n                  children: statusInfo.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"account-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Currency: \", account.currency]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Timezone: \", account.timezone_name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 23\n              }, this)]\n            }, account.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 21\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"no-data\",\n          children: \"No ad accounts found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"data-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(Users, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this), \"Pages (\", facebookStatus.pages.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this), facebookStatus.pages.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pages-list\",\n          children: facebookStatus.pages.map(page => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"page-item\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"page-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: page.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"page-id\",\n                children: [\"ID: \", page.id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"page-category\",\n                children: page.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 21\n            }, this)\n          }, page.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"no-data\",\n          children: \"No pages found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadFacebookData,\n        disabled: facebookStatus.loading,\n        className: \"refresh-btn\",\n        children: facebookStatus.loading ? 'Refreshing...' : 'Refresh Data'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(FacebookSection, \"0I4X/jUwT1nYIG7AOx1EYPhrKO0=\", false, function () {\n  return [useAuth];\n});\n_c = FacebookSection;\nexport default FacebookSection;\nvar _c;\n$RefreshReg$(_c, \"FacebookSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "facebookAPI", "useAuth", "toast", "Facebook", "Users", "CreditCard", "AlertCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "FacebookSection", "_s", "isAuthenticated", "facebookStatus", "setFacebookStatus", "connected", "loading", "accounts", "pages", "error", "loadFacebookData", "prev", "accountsResponse", "pagesResponse", "Promise", "all", "getAdAccounts", "catch", "data", "getPages", "length", "_error$response", "_error$response$data", "response", "message", "<PERSON><PERSON><PERSON>", "redirectUri", "window", "location", "origin", "getOAuthUrl", "oauthUrl", "href", "Error", "_error$response2", "_error$response2$data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onClick", "disabled", "map", "account", "getAccountStatus", "status", "text", "class", "statusInfo", "account_status", "name", "account_id", "currency", "timezone_name", "id", "page", "category", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/components/FacebookSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { facebookAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport toast from 'react-hot-toast';\nimport { Facebook, Users, CreditCard, AlertCircle, CheckCircle } from 'lucide-react';\n\nconst FacebookSection = () => {\n  const { isAuthenticated } = useAuth();\n  const [facebookStatus, setFacebookStatus] = useState({\n    connected: false,\n    loading: false,\n    accounts: [],\n    pages: [],\n    error: null\n  });\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadFacebookData();\n    }\n  }, [isAuthenticated]);\n\n  const loadFacebookData = async () => {\n    try {\n      setFacebookStatus(prev => ({ ...prev, loading: true, error: null }));\n      \n      const [accountsResponse, pagesResponse] = await Promise.all([\n        facebookAPI.getAdAccounts().catch(() => ({ data: [] })),\n        facebookAPI.getPages().catch(() => ({ data: [] }))\n      ]);\n\n      setFacebookStatus(prev => ({\n        ...prev,\n        connected: accountsResponse.data.length > 0 || pagesResponse.data.length > 0,\n        accounts: accountsResponse.data || [],\n        pages: pagesResponse.data || [],\n        loading: false\n      }));\n    } catch (error) {\n      setFacebookStatus(prev => ({\n        ...prev,\n        loading: false,\n        error: error.response?.data?.message || 'Failed to load Facebook data'\n      }));\n    }\n  };\n\n  const initiateOAuth = async () => {\n    try {\n      setFacebookStatus(prev => ({ ...prev, loading: true }));\n      \n      const redirectUri = `${window.location.origin}/facebook-callback`;\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      \n      if (response.data.oauthUrl) {\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL received');\n      }\n    } catch (error) {\n      setFacebookStatus(prev => ({ ...prev, loading: false }));\n      toast.error(error.response?.data?.message || 'Failed to initiate Facebook OAuth');\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"facebook-section\">\n        <h2>Facebook Integration</h2>\n        <div className=\"auth-required\">\n          <AlertCircle size={20} />\n          <p>Please log in to connect your Facebook account</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"facebook-section\">\n      <h2>Facebook Integration</h2>\n      \n      <div className=\"facebook-status\">\n        <div className=\"status-header\">\n          <Facebook size={20} />\n          <span>Connection Status</span>\n          {facebookStatus.connected ? (\n            <CheckCircle size={16} className=\"status-icon connected\" />\n          ) : (\n            <AlertCircle size={16} className=\"status-icon disconnected\" />\n          )}\n        </div>\n        \n        <p className={`status-text ${facebookStatus.connected ? 'connected' : 'disconnected'}`}>\n          {facebookStatus.connected ? 'Connected to Facebook' : 'Not connected to Facebook'}\n        </p>\n\n        {facebookStatus.error && (\n          <div className=\"error-message\">\n            <AlertCircle size={16} />\n            <span>{facebookStatus.error}</span>\n          </div>\n        )}\n      </div>\n\n      {!facebookStatus.connected && (\n        <div className=\"oauth-section\">\n          <p>Connect your Facebook Business Manager account to start managing campaigns.</p>\n          <button \n            onClick={initiateOAuth}\n            disabled={facebookStatus.loading}\n            className=\"oauth-btn\"\n          >\n            <Facebook size={16} />\n            {facebookStatus.loading ? 'Connecting...' : 'Connect Facebook Account'}\n          </button>\n        </div>\n      )}\n\n      {facebookStatus.connected && (\n        <div className=\"facebook-data\">\n          <div className=\"data-section\">\n            <h3>\n              <CreditCard size={16} />\n              Ad Accounts ({facebookStatus.accounts.length})\n            </h3>\n            {facebookStatus.accounts.length > 0 ? (\n              <div className=\"accounts-list\">\n                {facebookStatus.accounts.map((account) => {\n                  // Convert numeric account status to readable string\n                  const getAccountStatus = (status) => {\n                    switch(status) {\n                      case 1: return { text: 'ACTIVE', class: 'active' };\n                      case 2: return { text: 'DISABLED', class: 'disabled' };\n                      case 3: return { text: 'UNSETTLED', class: 'unsettled' };\n                      case 7: return { text: 'PENDING_RISK_REVIEW', class: 'pending' };\n                      case 8: return { text: 'PENDING_SETTLEMENT', class: 'pending' };\n                      case 9: return { text: 'IN_GRACE_PERIOD', class: 'grace' };\n                      case 100: return { text: 'PENDING_CLOSURE', class: 'pending' };\n                      case 101: return { text: 'CLOSED', class: 'closed' };\n                      case 201: return { text: 'ANY_ACTIVE', class: 'active' };\n                      case 202: return { text: 'ANY_CLOSED', class: 'closed' };\n                      default: return { text: `STATUS_${status}`, class: 'unknown' };\n                    }\n                  };\n\n                  const statusInfo = getAccountStatus(account.account_status);\n\n                  return (\n                    <div key={account.id} className=\"account-item\">\n                      <div className=\"account-info\">\n                        <strong>{account.name}</strong>\n                        <span className=\"account-id\">ID: {account.account_id}</span>\n                        <span className={`account-status ${statusInfo.class}`}>\n                          {statusInfo.text}\n                        </span>\n                      </div>\n                      <div className=\"account-details\">\n                        <span>Currency: {account.currency}</span>\n                        <span>Timezone: {account.timezone_name}</span>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            ) : (\n              <p className=\"no-data\">No ad accounts found</p>\n            )}\n          </div>\n\n          <div className=\"data-section\">\n            <h3>\n              <Users size={16} />\n              Pages ({facebookStatus.pages.length})\n            </h3>\n            {facebookStatus.pages.length > 0 ? (\n              <div className=\"pages-list\">\n                {facebookStatus.pages.map((page) => (\n                  <div key={page.id} className=\"page-item\">\n                    <div className=\"page-info\">\n                      <strong>{page.name}</strong>\n                      <span className=\"page-id\">ID: {page.id}</span>\n                      <span className=\"page-category\">{page.category}</span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <p className=\"no-data\">No pages found</p>\n            )}\n          </div>\n\n          <button \n            onClick={loadFacebookData}\n            disabled={facebookStatus.loading}\n            className=\"refresh-btn\"\n          >\n            {facebookStatus.loading ? 'Refreshing...' : 'Refresh Data'}\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FacebookSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,EAAEC,WAAW,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErF,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC;EAAgB,CAAC,GAAGX,OAAO,CAAC,CAAC;EACrC,MAAM,CAACY,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC;IACnDiB,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;EAEFpB,SAAS,CAAC,MAAM;IACd,IAAIa,eAAe,EAAE;MACnBQ,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACR,eAAe,CAAC,CAAC;EAErB,MAAMQ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFN,iBAAiB,CAACO,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEL,OAAO,EAAE,IAAI;QAAEG,KAAK,EAAE;MAAK,CAAC,CAAC,CAAC;MAEpE,MAAM,CAACG,gBAAgB,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC1DzB,WAAW,CAAC0B,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO;QAAEC,IAAI,EAAE;MAAG,CAAC,CAAC,CAAC,EACvD5B,WAAW,CAAC6B,QAAQ,CAAC,CAAC,CAACF,KAAK,CAAC,OAAO;QAAEC,IAAI,EAAE;MAAG,CAAC,CAAC,CAAC,CACnD,CAAC;MAEFd,iBAAiB,CAACO,IAAI,KAAK;QACzB,GAAGA,IAAI;QACPN,SAAS,EAAEO,gBAAgB,CAACM,IAAI,CAACE,MAAM,GAAG,CAAC,IAAIP,aAAa,CAACK,IAAI,CAACE,MAAM,GAAG,CAAC;QAC5Eb,QAAQ,EAAEK,gBAAgB,CAACM,IAAI,IAAI,EAAE;QACrCV,KAAK,EAAEK,aAAa,CAACK,IAAI,IAAI,EAAE;QAC/BZ,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdL,iBAAiB,CAACO,IAAI;QAAA,IAAAU,eAAA,EAAAC,oBAAA;QAAA,OAAK;UACzB,GAAGX,IAAI;UACPL,OAAO,EAAE,KAAK;UACdG,KAAK,EAAE,EAAAY,eAAA,GAAAZ,KAAK,CAACc,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBH,IAAI,cAAAI,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAI;QAC1C,CAAC;MAAA,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFrB,iBAAiB,CAACO,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEL,OAAO,EAAE;MAAK,CAAC,CAAC,CAAC;MAEvD,MAAMoB,WAAW,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,oBAAoB;MACjE,MAAMN,QAAQ,GAAG,MAAMjC,WAAW,CAACwC,WAAW,CAACJ,WAAW,CAAC;MAE3D,IAAIH,QAAQ,CAACL,IAAI,CAACa,QAAQ,EAAE;QAC1BJ,MAAM,CAACC,QAAQ,CAACI,IAAI,GAAGT,QAAQ,CAACL,IAAI,CAACa,QAAQ;MAC/C,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAAC,uBAAuB,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOxB,KAAK,EAAE;MAAA,IAAAyB,gBAAA,EAAAC,qBAAA;MACd/B,iBAAiB,CAACO,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEL,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;MACxDd,KAAK,CAACiB,KAAK,CAAC,EAAAyB,gBAAA,GAAAzB,KAAK,CAACc,QAAQ,cAAAW,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBX,OAAO,KAAI,mCAAmC,CAAC;IACnF;EACF,CAAC;EAED,IAAI,CAACtB,eAAe,EAAE;IACpB,oBACEH,OAAA;MAAKqC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BtC,OAAA;QAAAsC,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7B1C,OAAA;QAAKqC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BtC,OAAA,CAACH,WAAW;UAAC8C,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzB1C,OAAA;UAAAsC,QAAA,EAAG;QAA8C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE1C,OAAA;IAAKqC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BtC,OAAA;MAAAsC,QAAA,EAAI;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE7B1C,OAAA;MAAKqC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BtC,OAAA;QAAKqC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BtC,OAAA,CAACN,QAAQ;UAACiD,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtB1C,OAAA;UAAAsC,QAAA,EAAM;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC7BtC,cAAc,CAACE,SAAS,gBACvBN,OAAA,CAACF,WAAW;UAAC6C,IAAI,EAAE,EAAG;UAACN,SAAS,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE3D1C,OAAA,CAACH,WAAW;UAAC8C,IAAI,EAAE,EAAG;UAACN,SAAS,EAAC;QAA0B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC9D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN1C,OAAA;QAAGqC,SAAS,EAAE,eAAejC,cAAc,CAACE,SAAS,GAAG,WAAW,GAAG,cAAc,EAAG;QAAAgC,QAAA,EACpFlC,cAAc,CAACE,SAAS,GAAG,uBAAuB,GAAG;MAA2B;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF,CAAC,EAEHtC,cAAc,CAACM,KAAK,iBACnBV,OAAA;QAAKqC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BtC,OAAA,CAACH,WAAW;UAAC8C,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzB1C,OAAA;UAAAsC,QAAA,EAAOlC,cAAc,CAACM;QAAK;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL,CAACtC,cAAc,CAACE,SAAS,iBACxBN,OAAA;MAAKqC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BtC,OAAA;QAAAsC,QAAA,EAAG;MAA2E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAClF1C,OAAA;QACE4C,OAAO,EAAElB,aAAc;QACvBmB,QAAQ,EAAEzC,cAAc,CAACG,OAAQ;QACjC8B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAErBtC,OAAA,CAACN,QAAQ;UAACiD,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrBtC,cAAc,CAACG,OAAO,GAAG,eAAe,GAAG,0BAA0B;MAAA;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,EAEAtC,cAAc,CAACE,SAAS,iBACvBN,OAAA;MAAKqC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BtC,OAAA;QAAKqC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtC,OAAA;UAAAsC,QAAA,gBACEtC,OAAA,CAACJ,UAAU;YAAC+C,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBACX,EAACtC,cAAc,CAACI,QAAQ,CAACa,MAAM,EAAC,GAC/C;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACJtC,cAAc,CAACI,QAAQ,CAACa,MAAM,GAAG,CAAC,gBACjCrB,OAAA;UAAKqC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BlC,cAAc,CAACI,QAAQ,CAACsC,GAAG,CAAEC,OAAO,IAAK;YACxC;YACA,MAAMC,gBAAgB,GAAIC,MAAM,IAAK;cACnC,QAAOA,MAAM;gBACX,KAAK,CAAC;kBAAE,OAAO;oBAAEC,IAAI,EAAE,QAAQ;oBAAEC,KAAK,EAAE;kBAAS,CAAC;gBAClD,KAAK,CAAC;kBAAE,OAAO;oBAAED,IAAI,EAAE,UAAU;oBAAEC,KAAK,EAAE;kBAAW,CAAC;gBACtD,KAAK,CAAC;kBAAE,OAAO;oBAAED,IAAI,EAAE,WAAW;oBAAEC,KAAK,EAAE;kBAAY,CAAC;gBACxD,KAAK,CAAC;kBAAE,OAAO;oBAAED,IAAI,EAAE,qBAAqB;oBAAEC,KAAK,EAAE;kBAAU,CAAC;gBAChE,KAAK,CAAC;kBAAE,OAAO;oBAAED,IAAI,EAAE,oBAAoB;oBAAEC,KAAK,EAAE;kBAAU,CAAC;gBAC/D,KAAK,CAAC;kBAAE,OAAO;oBAAED,IAAI,EAAE,iBAAiB;oBAAEC,KAAK,EAAE;kBAAQ,CAAC;gBAC1D,KAAK,GAAG;kBAAE,OAAO;oBAAED,IAAI,EAAE,iBAAiB;oBAAEC,KAAK,EAAE;kBAAU,CAAC;gBAC9D,KAAK,GAAG;kBAAE,OAAO;oBAAED,IAAI,EAAE,QAAQ;oBAAEC,KAAK,EAAE;kBAAS,CAAC;gBACpD,KAAK,GAAG;kBAAE,OAAO;oBAAED,IAAI,EAAE,YAAY;oBAAEC,KAAK,EAAE;kBAAS,CAAC;gBACxD,KAAK,GAAG;kBAAE,OAAO;oBAAED,IAAI,EAAE,YAAY;oBAAEC,KAAK,EAAE;kBAAS,CAAC;gBACxD;kBAAS,OAAO;oBAAED,IAAI,EAAE,UAAUD,MAAM,EAAE;oBAAEE,KAAK,EAAE;kBAAU,CAAC;cAChE;YACF,CAAC;YAED,MAAMC,UAAU,GAAGJ,gBAAgB,CAACD,OAAO,CAACM,cAAc,CAAC;YAE3D,oBACErD,OAAA;cAAsBqC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC5CtC,OAAA;gBAAKqC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BtC,OAAA;kBAAAsC,QAAA,EAASS,OAAO,CAACO;gBAAI;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAC/B1C,OAAA;kBAAMqC,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,MAAI,EAACS,OAAO,CAACQ,UAAU;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5D1C,OAAA;kBAAMqC,SAAS,EAAE,kBAAkBe,UAAU,CAACD,KAAK,EAAG;kBAAAb,QAAA,EACnDc,UAAU,CAACF;gBAAI;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN1C,OAAA;gBAAKqC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BtC,OAAA;kBAAAsC,QAAA,GAAM,YAAU,EAACS,OAAO,CAACS,QAAQ;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzC1C,OAAA;kBAAAsC,QAAA,GAAM,YAAU,EAACS,OAAO,CAACU,aAAa;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA,GAXEK,OAAO,CAACW,EAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYf,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAEN1C,OAAA;UAAGqC,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAC/C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN1C,OAAA;QAAKqC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtC,OAAA;UAAAsC,QAAA,gBACEtC,OAAA,CAACL,KAAK;YAACgD,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WACZ,EAACtC,cAAc,CAACK,KAAK,CAACY,MAAM,EAAC,GACtC;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACJtC,cAAc,CAACK,KAAK,CAACY,MAAM,GAAG,CAAC,gBAC9BrB,OAAA;UAAKqC,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBlC,cAAc,CAACK,KAAK,CAACqC,GAAG,CAAEa,IAAI,iBAC7B3D,OAAA;YAAmBqC,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtCtC,OAAA;cAAKqC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBtC,OAAA;gBAAAsC,QAAA,EAASqB,IAAI,CAACL;cAAI;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAC5B1C,OAAA;gBAAMqC,SAAS,EAAC,SAAS;gBAAAC,QAAA,GAAC,MAAI,EAACqB,IAAI,CAACD,EAAE;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9C1C,OAAA;gBAAMqC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEqB,IAAI,CAACC;cAAQ;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC,GALEiB,IAAI,CAACD,EAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAEN1C,OAAA;UAAGqC,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACzC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN1C,OAAA;QACE4C,OAAO,EAAEjC,gBAAiB;QAC1BkC,QAAQ,EAAEzC,cAAc,CAACG,OAAQ;QACjC8B,SAAS,EAAC,aAAa;QAAAC,QAAA,EAEtBlC,cAAc,CAACG,OAAO,GAAG,eAAe,GAAG;MAAc;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxC,EAAA,CApMID,eAAe;EAAA,QACST,OAAO;AAAA;AAAAqE,EAAA,GAD/B5D,eAAe;AAsMrB,eAAeA,eAAe;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}