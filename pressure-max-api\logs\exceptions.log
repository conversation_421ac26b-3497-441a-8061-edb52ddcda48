{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:12:05 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 29816,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119455744,
      heapTotal: 76582912,
      heapUsed: 50665248,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127540.234 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:12:05'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:12:26 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 23840,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119627776,
      heapTotal: 76582912,
      heapUsed: 50472040,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127561.843 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:12:26'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:12:44 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 21416,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119537664,
      heapTotal: 76320768,
      heapUsed: 49878536,
      external: 2332419,
      arrayBuffers: 16795
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127580.078 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:12:45'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:12:55 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 24896,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 118714368,
      heapTotal: 75534336,
      heapUsed: 50986064,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127590.953 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:12:55'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:13:08 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 30112,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119099392,
      heapTotal: 75796480,
      heapUsed: 50059304,
      external: 2332419,
      arrayBuffers: 16795
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127603.609 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:13:08'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:13:30 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 9176,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120348672,
      heapTotal: 77107200,
      heapUsed: 49906280,
      external: 2307206,
      arrayBuffers: 16620
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127625.734 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:13:30'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:15:07 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 16988,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119246848,
      heapTotal: 76058624,
      heapUsed: 50918584,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127722.593 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:15:07'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:20:53 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 30772,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 118685696,
      heapTotal: 75796480,
      heapUsed: 50326840,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 128068.25 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:20:53'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:22:04 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 27112,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 121147392,
      heapTotal: 76582912,
      heapUsed: 50542264,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 128139.671 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:22:04'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:22:22 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 19796,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120819712,
      heapTotal: 76058624,
      heapUsed: 50878808,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 128157.375 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:22:22'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:22:36 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 1384,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 121303040,
      heapTotal: 76320768,
      heapUsed: 50560136,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 128171.546 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:22:36'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:38:36 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 18908,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120778752,
      heapTotal: 75796480,
      heapUsed: 51008912,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 129131.718 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:38:36'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:38:55 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 17624,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120549376,
      heapTotal: 75796480,
      heapUsed: 50803600,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 129150.671 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:38:55'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:39:18 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 32644,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120721408,
      heapTotal: 75796480,
      heapUsed: 50710040,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 129173.625 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:39:18'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:51:13 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 15876,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120946688,
      heapTotal: 76058624,
      heapUsed: 50440888,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 129888.156 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:51:13'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:51:26 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 31984,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120459264,
      heapTotal: 75534336,
      heapUsed: 50982440,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 129901.875 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:51:26'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:51:38 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 25252,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120709120,
      heapTotal: 76058624,
      heapUsed: 50608288,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 129914.062 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:51:38'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:52:01 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 19664,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 121339904,
      heapTotal: 76582912,
      heapUsed: 50730272,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 129936.5 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:52:01'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:52:17 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 3248,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 121401344,
      heapTotal: 76320768,
      heapUsed: 50715912,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 129952.453 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:52:17'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:58:01 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 18044,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 121544704,
      heapTotal: 76320768,
      heapUsed: 50492728,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 130296.14 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:58:01'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:02:45 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 16104,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 121077760,
      heapTotal: 76582912,
      heapUsed: 49960432,
      external: 2332419,
      arrayBuffers: 16795
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 130580.484 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:02:45'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:03:11 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 7408,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120643584,
      heapTotal: 76058624,
      heapUsed: 50738920,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 130606.718 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:03:11'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:11:24 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 16972,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119869440,
      heapTotal: 76058624,
      heapUsed: 50699344,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131099.671 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:11:24'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:11:49 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 26128,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119631872,
      heapTotal: 75796480,
      heapUsed: 50259264,
      external: 2332418,
      arrayBuffers: 16796
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131124.328 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:11:49'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:12:14 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 31380,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119717888,
      heapTotal: 75796480,
      heapUsed: 50775048,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131149.718 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:12:14'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:12:42 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 32668,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120528896,
      heapTotal: 76582912,
      heapUsed: 50333184,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131178.015 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:12:42'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:13:47 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 24872,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120356864,
      heapTotal: 76582912,
      heapUsed: 50261640,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131242.671 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:13:47'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:14:24 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 32936,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 116547584,
      heapTotal: 76320768,
      heapUsed: 50692872,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131279.781 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:14:24'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:14:41 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 25504,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120414208,
      heapTotal: 76582912,
      heapUsed: 50295144,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131296.375 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:14:41'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:15:27 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 8364,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 117264384,
      heapTotal: 75534336,
      heapUsed: 50738728,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131342.875 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:15:27'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:15:38 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 7644,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119869440,
      heapTotal: 75796480,
      heapUsed: 50959240,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131354.015 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:15:38'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:15:49 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 20996,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119332864,
      heapTotal: 75796480,
      heapUsed: 50730184,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131364.484 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:15:49'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:16:00 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 22352,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120512512,
      heapTotal: 76582912,
      heapUsed: 50588648,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131375.312 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:16:00'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:16:11 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 28236,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120176640,
      heapTotal: 76058624,
      heapUsed: 50546200,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131386.234 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:16:11'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:16:21 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 27836,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119906304,
      heapTotal: 76058624,
      heapUsed: 50194632,
      external: 2332418,
      arrayBuffers: 16796
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131396.703 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:16:21'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:16:32 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 10504,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 123555840,
      heapTotal: 79466496,
      heapUsed: 48552728,
      external: 2332419,
      arrayBuffers: 16795
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131407.734 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:16:32'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:16:43 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 12188,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119767040,
      heapTotal: 75796480,
      heapUsed: 50678928,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131418.796 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:16:43'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:16:58 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 25500,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120254464,
      heapTotal: 76582912,
      heapUsed: 50014416,
      external: 2332419,
      arrayBuffers: 16795
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131433.609 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:16:58'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:17:08 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 2556,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119734272,
      heapTotal: 76058624,
      heapUsed: 50535760,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131443.609 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:17:08'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:17:19 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 9708,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120246272,
      heapTotal: 76582912,
      heapUsed: 50548192,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131454.765 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:17:19'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:17:30 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 27960,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 117510144,
      heapTotal: 75534336,
      heapUsed: 50567192,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131465.515 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:17:30'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:18:06 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 25132,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 118317056,
      heapTotal: 76320768,
      heapUsed: 50639416,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131501.453 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:18:06'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:18:18 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 17536,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 117649408,
      heapTotal: 75796480,
      heapUsed: 50367280,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131513.328 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:18:18'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:21:21 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 28092,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120438784,
      heapTotal: 76582912,
      heapUsed: 50720584,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131696.156 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:21:21'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:21:37 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 30780,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119787520,
      heapTotal: 75796480,
      heapUsed: 50649624,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131713.062 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:21:37'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:22:23 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 31564,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120528896,
      heapTotal: 76582912,
      heapUsed: 50552984,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131758.437 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:22:23'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:22:38 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 33204,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119459840,
      heapTotal: 75534336,
      heapUsed: 50337496,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131773.437 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:22:38'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:23:56 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 20612,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119783424,
      heapTotal: 75796480,
      heapUsed: 51048032,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131851.187 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:23:56'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:24:22 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 15956,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 118497280,
      heapTotal: 76582912,
      heapUsed: 50438608,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131877.875 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:24:22'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:25:10 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 27892,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119537664,
      heapTotal: 75796480,
      heapUsed: 50618792,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 131926.046 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:25:10'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:35:03 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 29384,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120385536,
      heapTotal: 76582912,
      heapUsed: 50192432,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 132518.484 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:35:03'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:35:17 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 15472,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119062528,
      heapTotal: 75796480,
      heapUsed: 50481712,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 132532.656 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:35:17'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:35:31 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 22964,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120012800,
      heapTotal: 76320768,
      heapUsed: 50445192,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 132547.062 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:35:31'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:36:12 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 23736,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 123367424,
      heapTotal: 79728640,
      heapUsed: 48264744,
      external: 2332419,
      arrayBuffers: 16795
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 132587.296 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:36:12'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:36:39 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 20240,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120381440,
      heapTotal: 76582912,
      heapUsed: 50483208,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 132614.734 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:36:39'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:37:30 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 25924,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119332864,
      heapTotal: 76320768,
      heapUsed: 50213640,
      external: 2332418,
      arrayBuffers: 16796
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 132665.625 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:37:30'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:37:40 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 24184,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 117673984,
      heapTotal: 76320768,
      heapUsed: 50860672,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 132675.421 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:37:40'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:37:52 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 6852,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119263232,
      heapTotal: 76058624,
      heapUsed: 50839208,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 132687.781 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:37:52'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:38:43 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 18452,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 118972416,
      heapTotal: 75796480,
      heapUsed: 50845328,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 132738.968 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:38:43'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:44:11 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 26860,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 118673408,
      heapTotal: 75534336,
      heapUsed: 50990880,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 133066.312 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:44:11'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 11:44:38 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 18888,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 118800384,
      heapTotal: 75534336,
      heapUsed: 50695624,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 133093.437 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 11:44:38'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 12:14:34 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 15584,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 116346880,
      heapTotal: 76058624,
      heapUsed: 50452232,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 134889.484 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:14:34'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 12:14:52 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 29524,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 117014528,
      heapTotal: 76582912,
      heapUsed: 49883704,
      external: 2307206,
      arrayBuffers: 16620
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 134907.484 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:14:52'
}
{
  error: C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29
  const emailTransporter = nodemailer.createTransporter(config.email);
                                      ^
  
  TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29\n' +
    'const emailTransporter = nodemailer.createTransporter(config.email);\n' +
    '                                    ^\n' +
    '\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29\n' +
    'const emailTransporter = nodemailer.createTransporter(config.email);\n' +
    '                                    ^\n' +
    '\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 12:15:07 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 13192,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [ 'C:\\Program Files\\nodejs\\node.exe' ],
    memoryUsage: {
      rss: 116973568,
      heapTotal: 76320768,
      heapUsed: 50276392,
      external: 2375482,
      arrayBuffers: 16621
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 134922.14 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:15:07'
}
{
  error: C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29
  const emailTransporter = nodemailer.createTransporter(config.email);
                                      ^
  
  TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29\n' +
    'const emailTransporter = nodemailer.createTransporter(config.email);\n' +
    '                                    ^\n' +
    '\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29\n' +
    'const emailTransporter = nodemailer.createTransporter(config.email);\n' +
    '                                    ^\n' +
    '\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 12:15:43 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 20504,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [ 'C:\\Program Files\\nodejs\\node.exe' ],
    memoryUsage: {
      rss: 114315264,
      heapTotal: 75796480,
      heapUsed: 49754040,
      external: 2375482,
      arrayBuffers: 16619
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 134958.093 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:15:43'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 12:16:10 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 14592,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 116850688,
      heapTotal: 76320768,
      heapUsed: 50035520,
      external: 2332419,
      arrayBuffers: 16795
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 134985.187 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:16:10'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 12:17:01 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 26956,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 116760576,
      heapTotal: 76320768,
      heapUsed: 50547992,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 135036.89 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:17:01'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 12:17:13 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 29812,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 116391936,
      heapTotal: 76058624,
      heapUsed: 50637720,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 135048.89 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:17:13'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 12:17:30 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 10752,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 116699136,
      heapTotal: 76058624,
      heapUsed: 50697944,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 135065.203 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:17:30'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 12:18:02 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 30660,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 116359168,
      heapTotal: 75796480,
      heapUsed: 50773120,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 135097.953 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:18:02'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 12:18:25 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 30772,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 113922048,
      heapTotal: 75796480,
      heapUsed: 49890360,
      external: 2307205,
      arrayBuffers: 16619
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 135120.75 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:18:25'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 12:19:20 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 34308,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 116158464,
      heapTotal: 75796480,
      heapUsed: 50587656,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 135175.546 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:19:20'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 12:19:20 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 25540,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119836672,
      heapTotal: 79728640,
      heapUsed: 48088600,
      external: 2332419,
      arrayBuffers: 16795
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 135175.562 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:19:20'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 12:19:29 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 16116,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 116920320,
      heapTotal: 76320768,
      heapUsed: 50381952,
      external: 2307205,
      arrayBuffers: 16621
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 135184.796 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:19:29'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 17:19:54 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 31496,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 121643008,
      heapTotal: 79466496,
      heapUsed: 48604448,
      external: 2332419,
      arrayBuffers: 16795
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 153209.515 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:19:54'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 17:20:48 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 21016,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 121036800,
      heapTotal: 76582912,
      heapUsed: 50900112,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 153263.796 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:20:48'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 17:21:02 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 16184,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120406016,
      heapTotal: 75796480,
      heapUsed: 50937872,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 153278.015 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:21:02'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 17:21:16 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 33532,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120799232,
      heapTotal: 76320768,
      heapUsed: 50568816,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 153291.093 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:21:16'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 17:21:40 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 3564,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120655872,
      heapTotal: 76058624,
      heapUsed: 50587928,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 153315.578 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:21:40'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 17:21:56 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 28596,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120627200,
      heapTotal: 76058624,
      heapUsed: 50247576,
      external: 2332418,
      arrayBuffers: 16796
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 153331.796 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:21:56'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 17:22:29 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 31828,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 123437056,
      heapTotal: 79466496,
      heapUsed: 48098728,
      external: 2307205,
      arrayBuffers: 16619
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 153364.843 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:22:29'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 17:22:55 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 32524,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120569856,
      heapTotal: 75796480,
      heapUsed: 50914984,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 153390.843 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:22:55'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 17:23:18 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 34276,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 121229312,
      heapTotal: 76582912,
      heapUsed: 50832272,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 153413.296 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:23:18'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 17:23:30 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 15512,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120225792,
      heapTotal: 75796480,
      heapUsed: 50778392,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 153425.843 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:23:30'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 17:24:07 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 28664,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 121208832,
      heapTotal: 76845056,
      heapUsed: 49859408,
      external: 2307205,
      arrayBuffers: 16619
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 153462.14 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:24:07'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 17:24:23 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 32716,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120922112,
      heapTotal: 76582912,
      heapUsed: 50286800,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 153478.609 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:24:23'
}
