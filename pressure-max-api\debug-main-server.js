console.log('🔍 Starting debug of main server...');

try {
  console.log('1. Loading express...');
  const express = require('express');
  console.log('✅ Express loaded');

  console.log('2. Loading http...');
  const http = require('http');
  console.log('✅ HTTP loaded');

  console.log('3. Loading config...');
  const config = require('./src/config/config');
  console.log('✅ Config loaded');

  console.log('4. Loading logger...');
  const logger = require('./src/config/logger');
  console.log('✅ Logger loaded');

  console.log('5. Loading database...');
  const database = require('./src/config/database');
  console.log('✅ Database module loaded');

  console.log('6. Loading redis...');
  const redis = require('./src/config/redis');
  console.log('✅ Redis module loaded');

  console.log('7. Loading middleware...');
  const errorHandler = require('./src/middleware/errorHandler');
  const authMiddleware = require('./src/middleware/auth');
  console.log('✅ Middleware loaded');

  console.log('8. Loading routes...');
  const authRoutes = require('./src/routes/auth');
  console.log('✅ Auth routes loaded');
  
  const userRoutes = require('./src/routes/users');
  console.log('✅ User routes loaded');
  
  const facebookRoutes = require('./src/routes/facebook');
  console.log('✅ Facebook routes loaded');
  
  const templateRoutes = require('./src/routes/templates');
  console.log('✅ Template routes loaded');
  
  const campaignRoutes = require('./src/routes/campaigns');
  console.log('✅ Campaign routes loaded');
  
  const vapiRoutes = require('./src/routes/vapi');
  console.log('✅ VAPI routes loaded');
  
  const leadRoutes = require('./src/routes/leads');
  console.log('✅ Lead routes loaded');
  
  const webhookRoutes = require('./src/routes/webhooks');
  console.log('✅ Webhook routes loaded');
  
  const analyticsRoutes = require('./src/routes/analytics');
  console.log('✅ Analytics routes loaded');

  console.log('9. Loading services...');
  const webhookService = require('./src/services/webhookService');
  console.log('✅ Webhook service loaded');
  
  const notificationService = require('./src/services/notificationService');
  console.log('✅ Notification service loaded');

  console.log('10. Loading main server class...');
  const PressureMaxServer = require('./src/server');
  console.log('✅ Server class loaded');

  console.log('🎉 All modules loaded successfully!');
  console.log('The issue is likely in the server initialization or startup process.');

} catch (error) {
  console.error('❌ Error loading modules:', error);
  console.error('Stack trace:', error.stack);
}
