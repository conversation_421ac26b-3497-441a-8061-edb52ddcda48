{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst CircleDotDashed = createLucideIcon(\"CircleDotDashed\", [[\"path\", {\n  d: \"M10.1 2.18a9.93 9.93 0 0 1 3.8 0\",\n  key: \"1qdqn0\"\n}], [\"path\", {\n  d: \"M17.6 3.71a9.95 9.95 0 0 1 2.69 2.7\",\n  key: \"1bq7p6\"\n}], [\"path\", {\n  d: \"M21.82 10.1a9.93 9.93 0 0 1 0 3.8\",\n  key: \"1rlaqf\"\n}], [\"path\", {\n  d: \"M20.29 17.6a9.95 9.95 0 0 1-2.7 2.69\",\n  key: \"1xk03u\"\n}], [\"path\", {\n  d: \"M13.9 21.82a9.94 9.94 0 0 1-3.8 0\",\n  key: \"l7re25\"\n}], [\"path\", {\n  d: \"M6.4 20.29a9.95 9.95 0 0 1-2.69-2.7\",\n  key: \"1v18p6\"\n}], [\"path\", {\n  d: \"M2.18 13.9a9.93 9.93 0 0 1 0-3.8\",\n  key: \"xdo6bj\"\n}], [\"path\", {\n  d: \"M3.71 6.4a9.95 9.95 0 0 1 2.7-2.69\",\n  key: \"1jjmaz\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"1\",\n  key: \"41hilf\"\n}]]);\nexport { CircleDotDashed as default };", "map": {"version": 3, "names": ["CircleDotDashed", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\node_modules\\lucide-react\\src\\icons\\circle-dot-dashed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CircleDotDashed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuMSAyLjE4YTkuOTMgOS45MyAwIDAgMSAzLjggMCIgLz4KICA8cGF0aCBkPSJNMTcuNiAzLjcxYTkuOTUgOS45NSAwIDAgMSAyLjY5IDIuNyIgLz4KICA8cGF0aCBkPSJNMjEuODIgMTAuMWE5LjkzIDkuOTMgMCAwIDEgMCAzLjgiIC8+CiAgPHBhdGggZD0iTTIwLjI5IDE3LjZhOS45NSA5Ljk1IDAgMCAxLTIuNyAyLjY5IiAvPgogIDxwYXRoIGQ9Ik0xMy45IDIxLjgyYTkuOTQgOS45NCAwIDAgMS0zLjggMCIgLz4KICA8cGF0aCBkPSJNNi40IDIwLjI5YTkuOTUgOS45NSAwIDAgMS0yLjY5LTIuNyIgLz4KICA8cGF0aCBkPSJNMi4xOCAxMy45YTkuOTMgOS45MyAwIDAgMSAwLTMuOCIgLz4KICA8cGF0aCBkPSJNMy43MSA2LjRhOS45NSA5Ljk1IDAgMCAxIDIuNy0yLjY5IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-dot-dashed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleDotDashed = createLucideIcon('CircleDotDashed', [\n  ['path', { d: 'M10.1 2.18a9.93 9.93 0 0 1 3.8 0', key: '1qdqn0' }],\n  ['path', { d: 'M17.6 3.71a9.95 9.95 0 0 1 2.69 2.7', key: '1bq7p6' }],\n  ['path', { d: 'M21.82 10.1a9.93 9.93 0 0 1 0 3.8', key: '1rlaqf' }],\n  ['path', { d: 'M20.29 17.6a9.95 9.95 0 0 1-2.7 2.69', key: '1xk03u' }],\n  ['path', { d: 'M13.9 21.82a9.94 9.94 0 0 1-3.8 0', key: 'l7re25' }],\n  ['path', { d: 'M6.4 20.29a9.95 9.95 0 0 1-2.69-2.7', key: '1v18p6' }],\n  ['path', { d: 'M2.18 13.9a9.93 9.93 0 0 1 0-3.8', key: 'xdo6bj' }],\n  ['path', { d: 'M3.71 6.4a9.95 9.95 0 0 1 2.7-2.69', key: '1jjmaz' }],\n  ['circle', { cx: '12', cy: '12', r: '1', key: '41hilf' }],\n]);\n\nexport default CircleDotDashed;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,eAAA,GAAkBC,gBAAA,CAAiB,iBAAmB,GAC1D,CAAC,MAAQ;EAAEC,CAAA,EAAG,kCAAoC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAED,CAAA,EAAG,mCAAqC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,MAAQ;EAAED,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,MAAQ;EAAED,CAAA,EAAG,mCAAqC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAED,CAAA,EAAG,kCAAoC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,MAAQ;EAAED,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}