const axios = require('axios');

async function testCampaignCreation() {
  try {
    console.log('Testing campaign creation...');
    
    const response = await axios.post('http://localhost:3000/api/v1/facebook/campaigns', {
      adAccountId: 'act_263173616383414',
      name: 'Test Enhanced Campaign Creation',
      objective: 'OUTCOME_LEADS'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Campaign creation response:', response.data);
  } catch (error) {
    console.error('Campaign creation error:', error.response?.data || error.message);
  }
}

testCampaignCreation();
