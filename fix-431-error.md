# Fix 431 "Request Header Fields Too Large" Error

The 431 error occurs when request headers exceed the server's limit. This is commonly caused by:

## 🔍 Common Causes:
1. **Large cookies** (authentication tokens, session data)
2. **Large authorization headers** (JWT tokens)
3. **Browser cache issues**
4. **Multiple authentication attempts**

## 🛠️ Quick Fixes:

### 1. Clear Browser Data (Recommended)
**Chrome/Edge:**
1. Press `Ctrl + Shift + Delete`
2. Select "All time" 
3. Check "Cookies and other site data"
4. Click "Clear data"

**Firefox:**
1. Press `Ctrl + Shift + Delete`
2. Select "Everything"
3. Check "Cookies" and "Site Data"
4. Click "Clear Now"

### 2. Clear Specific Site Data
**Chrome/Edge:**
1. Go to `chrome://settings/content/cookies`
2. Click "See all cookies and site data"
3. Search for "localhost"
4. Delete all localhost entries

### 3. Use Incognito/Private Mode
- Open a new incognito/private window
- Try accessing the application

### 4. Clear Local Storage (Developer Tools)
1. Press `F12` to open Developer Tools
2. Go to "Application" tab
3. Under "Storage" → "Local Storage" → delete localhost entries
4. Under "Storage" → "Session Storage" → delete localhost entries
5. Under "Storage" → "Cookies" → delete localhost cookies

## 🔧 Server-Side Fixes Applied:

The server has been updated with:
- ✅ Increased header size limit (16KB)
- ✅ Better error handling for 431 errors
- ✅ Optimized CORS configuration
- ✅ Reduced security header overhead

## 🚀 Restart Instructions:

After clearing browser data:

1. **Stop the server:**
   ```bash
   stop-all.bat
   ```

2. **Start the server:**
   ```bash
   start-dev.bat
   ```

3. **Clear browser cache and try again**

## 🐛 If Problem Persists:

### Check Request Headers:
1. Open Developer Tools (`F12`)
2. Go to "Network" tab
3. Try to make a request
4. Click on the failed request
5. Check "Request Headers" size

### Manual Cookie Cleanup:
```javascript
// Run this in browser console (F12 → Console)
// Clear all cookies for localhost
document.cookie.split(";").forEach(function(c) { 
    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
});

// Clear localStorage
localStorage.clear();

// Clear sessionStorage
sessionStorage.clear();

console.log("Browser storage cleared!");
```

### Check for Large JWT Tokens:
If using JWT authentication, check token size:
```javascript
// In browser console
const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
if (token) {
    console.log('Token size:', token.length, 'characters');
    console.log('Token size:', new Blob([token]).size, 'bytes');
}
```

## 📞 Still Having Issues?

1. **Try a different browser**
2. **Disable browser extensions**
3. **Check if antivirus/firewall is interfering**
4. **Restart your computer** (clears all browser processes)

## 🔍 Technical Details:

The server now supports:
- **Header limit:** 16KB (increased from 8KB default)
- **Request timeout:** 30 seconds
- **Keep-alive timeout:** 5 seconds
- **Better error messages** for debugging

## ⚡ Prevention:

To prevent this issue in the future:
- Regular browser cache clearing
- Avoid storing large data in cookies
- Use proper session management
- Monitor JWT token sizes
