import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refreshToken,
          });

          const { accessToken, refreshToken: newRefreshToken } = response.data.tokens;
          localStorage.setItem('accessToken', accessToken);
          localStorage.setItem('refreshToken', newRefreshToken);

          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        window.location.reload();
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (userData) => api.post('/auth/register', userData),
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  refresh: (refreshToken) => api.post('/auth/refresh', { refreshToken }),
};

// User API
export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data) => api.put('/users/profile', data),
  getTeam: () => api.get('/users/team'),
};

// Facebook API
export const facebookAPI = {
  getOAuthUrl: (redirectUri) => api.get('/facebook/oauth-url', { params: { redirectUri } }),
  handleOAuthCallback: (code, state) => api.post('/facebook/oauth-callback', { code, state }),
  getAdAccounts: () => api.get('/facebook/ad-accounts'),
  getPages: () => api.get('/facebook/pages'),
  createCampaign: (campaignData) =>
    api.post('/facebook/campaigns', campaignData),
  getCampaigns: (adAccountId) => api.get(`/facebook/campaigns/${adAccountId}`),
  getCampaignInsights: (campaignId, dateRange) => 
    api.get(`/facebook/campaigns/${campaignId}/insights`, { params: dateRange }),
};

// Campaign API
export const campaignAPI = {
  list: (params) => api.get('/campaigns', { params }),
  create: (data) => api.post('/campaigns', data),
  get: (id) => api.get(`/campaigns/${id}`),
  update: (id, data) => api.put(`/campaigns/${id}`, data),
  delete: (id) => api.delete(`/campaigns/${id}`),
};

// Health check
export const healthCheck = () => axios.get('http://localhost:3000/health');

export default api;
