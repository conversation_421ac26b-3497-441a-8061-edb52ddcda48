{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\components\\\\AuthSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { User, LogOut, Eye, EyeOff, Facebook } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthSection = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    getToken\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n    if (code && state && !isAuthenticated) {\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n  const onLogin = async data => {\n    setLoading(true);\n    await login(data);\n    setLoading(false);\n  };\n  const onRegister = async data => {\n    setLoading(true);\n    await register(data);\n    setLoading(false);\n  };\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}${window.location.pathname}`;\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      if (response.data.oauthUrl) {\n        // Store state for verification\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL received');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n\n      // Verify state parameter\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      if (!storedState || storedState !== state) {\n        throw new Error('Invalid state parameter');\n      }\n\n      // Clean up stored state\n      localStorage.removeItem('facebook_oauth_state');\n      const redirectUri = `${window.location.origin}${window.location.pathname}`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      if (response.data.user && response.data.tokens) {\n        // Store tokens and user data\n        localStorage.setItem('accessToken', response.data.tokens.accessToken);\n        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n\n        // Update auth context\n        window.location.reload(); // Simple way to update the auth context\n\n        toast.success('Successfully logged in with Facebook!');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n\n      // Clean up URL parameters\n      const url = new URL(window.location);\n      url.searchParams.delete('code');\n      url.searchParams.delete('state');\n      window.history.replaceState({}, document.title, url.toString());\n    }\n  };\n  if (isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Authentication Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-status authenticated\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: [/*#__PURE__*/_jsxDEV(User, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [user.firstName, \" \", user.lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 18\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Role: \", user.role]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"token-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"JWT Token:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowToken(!showToken),\n              className: \"toggle-token\",\n              children: showToken ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 30\n              }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), showToken && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-display\",\n            children: /*#__PURE__*/_jsxDEV(\"code\", {\n              children: getToken()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: logout,\n          className: \"logout-btn\",\n          children: [/*#__PURE__*/_jsxDEV(LogOut, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), \"Logout\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Authentication\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'facebook' ? 'active' : '',\n        onClick: () => setActiveTab('facebook'),\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), \"Facebook Login\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'login' ? 'active' : '',\n        onClick: () => setActiveTab('login'),\n        children: \"Email Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'register' ? 'active' : '',\n        onClick: () => setActiveTab('register'),\n        children: \"Register\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), activeTab === 'facebook' ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"facebook-login-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"facebook-login-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Login with Facebook\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Connect your Facebook Business Manager account to access:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Ad account management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Campaign creation and monitoring\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Lead generation forms\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Marketing API permissions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 No separate email/password required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleFacebookLogin,\n        disabled: loading,\n        className: \"facebook-login-btn\",\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this), loading ? 'Connecting to Facebook...' : 'Login with Facebook Business Manager']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"facebook-note\",\n        children: [\"Using App ID: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: \"***************\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 27\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 55\n        }, this), \"This will automatically grant marketing API permissions.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 9\n    }, this) : activeTab === 'login' ? /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: loginForm.handleSubmit(onLogin),\n      className: \"auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...loginForm.register('email', {\n            required: 'Email is required'\n          }),\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), loginForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: loginForm.formState.errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Password:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...loginForm.register('password', {\n            required: 'Password is required'\n          }),\n          placeholder: \"Enter your password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this), loginForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: loginForm.formState.errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"submit-btn\",\n        children: loading ? 'Logging in...' : 'Login'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: registerForm.handleSubmit(onRegister),\n      className: \"auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"First Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...registerForm.register('firstName', {\n            required: 'First name is required'\n          }),\n          placeholder: \"Enter your first name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.firstName && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.firstName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Last Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...registerForm.register('lastName', {\n            required: 'Last name is required'\n          }),\n          placeholder: \"Enter your last name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.lastName && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.lastName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...registerForm.register('email', {\n            required: 'Email is required'\n          }),\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Password:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...registerForm.register('password', {\n            required: 'Password is required',\n            minLength: {\n              value: 8,\n              message: 'Password must be at least 8 characters'\n            }\n          }),\n          placeholder: \"Enter your password (min 8 characters)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Phone (optional):\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"tel\",\n          ...registerForm.register('phone'),\n          placeholder: \"Enter your phone number\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"submit-btn\",\n        children: loading ? 'Registering...' : 'Register'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthSection, \"J+0be1bUuf2hGRBagTwIZhTtrCY=\", false, function () {\n  return [useAuth, useForm, useForm];\n});\n_c = AuthSection;\nexport default AuthSection;\nvar _c;\n$RefreshReg$(_c, \"AuthSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useForm", "facebookAPI", "User", "LogOut", "Eye", "Eye<PERSON>ff", "Facebook", "toast", "jsxDEV", "_jsxDEV", "AuthSection", "_s", "user", "isAuthenticated", "login", "register", "logout", "getToken", "activeTab", "setActiveTab", "showToken", "setShowToken", "loading", "setLoading", "loginForm", "registerForm", "urlParams", "URLSearchParams", "window", "location", "search", "code", "get", "state", "handleFacebookCallback", "onLogin", "data", "onRegister", "handleFacebookLogin", "redirectUri", "origin", "pathname", "response", "getOAuthUrl", "oauthUrl", "localStorage", "setItem", "href", "Error", "error", "message", "storedState", "getItem", "removeItem", "handleOAuthCallback", "tokens", "accessToken", "refreshToken", "JSON", "stringify", "reload", "success", "url", "URL", "searchParams", "delete", "history", "replaceState", "document", "title", "toString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "firstName", "lastName", "email", "role", "onClick", "disabled", "onSubmit", "handleSubmit", "type", "required", "placeholder", "formState", "errors", "password", "<PERSON><PERSON><PERSON><PERSON>", "value", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/components/AuthSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { User, LogOut, Eye, EyeOff, Facebook } from 'lucide-react';\nimport toast from 'react-hot-toast';\n\nconst AuthSection = () => {\n  const { user, isAuthenticated, login, register, logout, getToken } = useAuth();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n\n    if (code && state && !isAuthenticated) {\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n\n  const onLogin = async (data) => {\n    setLoading(true);\n    await login(data);\n    setLoading(false);\n  };\n\n  const onRegister = async (data) => {\n    setLoading(true);\n    await register(data);\n    setLoading(false);\n  };\n\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}${window.location.pathname}`;\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n\n      if (response.data.oauthUrl) {\n        // Store state for verification\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL received');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n\n      // Verify state parameter\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      if (!storedState || storedState !== state) {\n        throw new Error('Invalid state parameter');\n      }\n\n      // Clean up stored state\n      localStorage.removeItem('facebook_oauth_state');\n\n      const redirectUri = `${window.location.origin}${window.location.pathname}`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n\n      if (response.data.user && response.data.tokens) {\n        // Store tokens and user data\n        localStorage.setItem('accessToken', response.data.tokens.accessToken);\n        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n\n        // Update auth context\n        window.location.reload(); // Simple way to update the auth context\n\n        toast.success('Successfully logged in with Facebook!');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n\n      // Clean up URL parameters\n      const url = new URL(window.location);\n      url.searchParams.delete('code');\n      url.searchParams.delete('state');\n      window.history.replaceState({}, document.title, url.toString());\n    }\n  };\n\n  if (isAuthenticated) {\n    return (\n      <div className=\"auth-section\">\n        <h2>Authentication Status</h2>\n        <div className=\"auth-status authenticated\">\n          <div className=\"user-info\">\n            <User size={20} />\n            <div>\n              <p><strong>{user.firstName} {user.lastName}</strong></p>\n              <p>{user.email}</p>\n              <p>Role: {user.role}</p>\n            </div>\n          </div>\n          \n          <div className=\"token-section\">\n            <div className=\"token-header\">\n              <span>JWT Token:</span>\n              <button \n                onClick={() => setShowToken(!showToken)}\n                className=\"toggle-token\"\n              >\n                {showToken ? <EyeOff size={16} /> : <Eye size={16} />}\n              </button>\n            </div>\n            {showToken && (\n              <div className=\"token-display\">\n                <code>{getToken()}</code>\n              </div>\n            )}\n          </div>\n\n          <button onClick={logout} className=\"logout-btn\">\n            <LogOut size={16} />\n            Logout\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"auth-section\">\n      <h2>Authentication</h2>\n      \n      <div className=\"auth-tabs\">\n        <button\n          className={activeTab === 'facebook' ? 'active' : ''}\n          onClick={() => setActiveTab('facebook')}\n        >\n          <Facebook size={16} />\n          Facebook Login\n        </button>\n        <button\n          className={activeTab === 'login' ? 'active' : ''}\n          onClick={() => setActiveTab('login')}\n        >\n          Email Login\n        </button>\n        <button\n          className={activeTab === 'register' ? 'active' : ''}\n          onClick={() => setActiveTab('register')}\n        >\n          Register\n        </button>\n      </div>\n\n      {activeTab === 'facebook' ? (\n        <div className=\"facebook-login-section\">\n          <div className=\"facebook-login-info\">\n            <h3>Login with Facebook</h3>\n            <p>Connect your Facebook Business Manager account to access:</p>\n            <ul>\n              <li>✅ Ad account management</li>\n              <li>✅ Campaign creation and monitoring</li>\n              <li>✅ Lead generation forms</li>\n              <li>✅ Marketing API permissions</li>\n              <li>✅ No separate email/password required</li>\n            </ul>\n          </div>\n\n          <button\n            onClick={handleFacebookLogin}\n            disabled={loading}\n            className=\"facebook-login-btn\"\n          >\n            <Facebook size={20} />\n            {loading ? 'Connecting to Facebook...' : 'Login with Facebook Business Manager'}\n          </button>\n\n          <p className=\"facebook-note\">\n            Using App ID: <code>***************</code><br/>\n            This will automatically grant marketing API permissions.\n          </p>\n        </div>\n      ) : activeTab === 'login' ? (\n        <form onSubmit={loginForm.handleSubmit(onLogin)} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>Email:</label>\n            <input\n              type=\"email\"\n              {...loginForm.register('email', { required: 'Email is required' })}\n              placeholder=\"Enter your email\"\n            />\n            {loginForm.formState.errors.email && (\n              <span className=\"error\">{loginForm.formState.errors.email.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password:</label>\n            <input\n              type=\"password\"\n              {...loginForm.register('password', { required: 'Password is required' })}\n              placeholder=\"Enter your password\"\n            />\n            {loginForm.formState.errors.password && (\n              <span className=\"error\">{loginForm.formState.errors.password.message}</span>\n            )}\n          </div>\n\n          <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n            {loading ? 'Logging in...' : 'Login'}\n          </button>\n        </form>\n      ) : (\n        <form onSubmit={registerForm.handleSubmit(onRegister)} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>First Name:</label>\n            <input\n              type=\"text\"\n              {...registerForm.register('firstName', { required: 'First name is required' })}\n              placeholder=\"Enter your first name\"\n            />\n            {registerForm.formState.errors.firstName && (\n              <span className=\"error\">{registerForm.formState.errors.firstName.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Last Name:</label>\n            <input\n              type=\"text\"\n              {...registerForm.register('lastName', { required: 'Last name is required' })}\n              placeholder=\"Enter your last name\"\n            />\n            {registerForm.formState.errors.lastName && (\n              <span className=\"error\">{registerForm.formState.errors.lastName.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Email:</label>\n            <input\n              type=\"email\"\n              {...registerForm.register('email', { required: 'Email is required' })}\n              placeholder=\"Enter your email\"\n            />\n            {registerForm.formState.errors.email && (\n              <span className=\"error\">{registerForm.formState.errors.email.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password:</label>\n            <input\n              type=\"password\"\n              {...registerForm.register('password', { \n                required: 'Password is required',\n                minLength: { value: 8, message: 'Password must be at least 8 characters' }\n              })}\n              placeholder=\"Enter your password (min 8 characters)\"\n            />\n            {registerForm.formState.errors.password && (\n              <span className=\"error\">{registerForm.formState.errors.password.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Phone (optional):</label>\n            <input\n              type=\"tel\"\n              {...registerForm.register('phone')}\n              placeholder=\"Enter your phone number\"\n            />\n          </div>\n\n          <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n            {loading ? 'Registering...' : 'Register'}\n          </button>\n        </form>\n      )}\n    </div>\n  );\n};\n\nexport default AuthSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,cAAc;AAClE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAC9E,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM2B,SAAS,GAAGxB,OAAO,CAAC,CAAC;EAC3B,MAAMyB,YAAY,GAAGzB,OAAO,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACd,MAAM4B,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,IAAI,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC;IAClC,MAAMC,KAAK,GAAGP,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC;IAEpC,IAAID,IAAI,IAAIE,KAAK,IAAI,CAACpB,eAAe,EAAE;MACrCqB,sBAAsB,CAACH,IAAI,EAAEE,KAAK,CAAC;IACrC;EACF,CAAC,EAAE,CAACpB,eAAe,CAAC,CAAC;EAErB,MAAMsB,OAAO,GAAG,MAAOC,IAAI,IAAK;IAC9Bb,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMT,KAAK,CAACsB,IAAI,CAAC;IACjBb,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMc,UAAU,GAAG,MAAOD,IAAI,IAAK;IACjCb,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMR,QAAQ,CAACqB,IAAI,CAAC;IACpBb,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMe,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMgB,WAAW,GAAG,GAAGX,MAAM,CAACC,QAAQ,CAACW,MAAM,GAAGZ,MAAM,CAACC,QAAQ,CAACY,QAAQ,EAAE;MAC1E,MAAMC,QAAQ,GAAG,MAAMzC,WAAW,CAAC0C,WAAW,CAACJ,WAAW,CAAC;MAE3D,IAAIG,QAAQ,CAACN,IAAI,CAACQ,QAAQ,EAAE;QAC1B;QACAC,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAEJ,QAAQ,CAACN,IAAI,CAACH,KAAK,CAAC;QACjEL,MAAM,CAACC,QAAQ,CAACkB,IAAI,GAAGL,QAAQ,CAACN,IAAI,CAACQ,QAAQ;MAC/C,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,uBAAuB,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd1B,UAAU,CAAC,KAAK,CAAC;MACjBhB,KAAK,CAAC0C,KAAK,CAAC,qCAAqC,GAAGA,KAAK,CAACC,OAAO,CAAC;IACpE;EACF,CAAC;EAED,MAAMhB,sBAAsB,GAAG,MAAAA,CAAOH,IAAI,EAAEE,KAAK,KAAK;IACpD,IAAI;MACFV,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM4B,WAAW,GAAGN,YAAY,CAACO,OAAO,CAAC,sBAAsB,CAAC;MAChE,IAAI,CAACD,WAAW,IAAIA,WAAW,KAAKlB,KAAK,EAAE;QACzC,MAAM,IAAIe,KAAK,CAAC,yBAAyB,CAAC;MAC5C;;MAEA;MACAH,YAAY,CAACQ,UAAU,CAAC,sBAAsB,CAAC;MAE/C,MAAMd,WAAW,GAAG,GAAGX,MAAM,CAACC,QAAQ,CAACW,MAAM,GAAGZ,MAAM,CAACC,QAAQ,CAACY,QAAQ,EAAE;MAC1E,MAAMC,QAAQ,GAAG,MAAMzC,WAAW,CAACqD,mBAAmB,CAACvB,IAAI,EAAEE,KAAK,EAAEM,WAAW,CAAC;MAEhF,IAAIG,QAAQ,CAACN,IAAI,CAACxB,IAAI,IAAI8B,QAAQ,CAACN,IAAI,CAACmB,MAAM,EAAE;QAC9C;QACAV,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEJ,QAAQ,CAACN,IAAI,CAACmB,MAAM,CAACC,WAAW,CAAC;QACrEX,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,QAAQ,CAACN,IAAI,CAACmB,MAAM,CAACE,YAAY,CAAC;QACvEZ,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEY,IAAI,CAACC,SAAS,CAACjB,QAAQ,CAACN,IAAI,CAACxB,IAAI,CAAC,CAAC;;QAEhE;QACAgB,MAAM,CAACC,QAAQ,CAAC+B,MAAM,CAAC,CAAC,CAAC,CAAC;;QAE1BrD,KAAK,CAACsD,OAAO,CAAC,uCAAuC,CAAC;MACxD;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd1B,UAAU,CAAC,KAAK,CAAC;MACjBhB,KAAK,CAAC0C,KAAK,CAAC,yBAAyB,GAAGA,KAAK,CAACC,OAAO,CAAC;;MAEtD;MACA,MAAMY,GAAG,GAAG,IAAIC,GAAG,CAACnC,MAAM,CAACC,QAAQ,CAAC;MACpCiC,GAAG,CAACE,YAAY,CAACC,MAAM,CAAC,MAAM,CAAC;MAC/BH,GAAG,CAACE,YAAY,CAACC,MAAM,CAAC,OAAO,CAAC;MAChCrC,MAAM,CAACsC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAEP,GAAG,CAACQ,QAAQ,CAAC,CAAC,CAAC;IACjE;EACF,CAAC;EAED,IAAIzD,eAAe,EAAE;IACnB,oBACEJ,OAAA;MAAK8D,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B/D,OAAA;QAAA+D,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BnE,OAAA;QAAK8D,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxC/D,OAAA;UAAK8D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/D,OAAA,CAACP,IAAI;YAAC2E,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClBnE,OAAA;YAAA+D,QAAA,gBACE/D,OAAA;cAAA+D,QAAA,eAAG/D,OAAA;gBAAA+D,QAAA,GAAS5D,IAAI,CAACkE,SAAS,EAAC,GAAC,EAAClE,IAAI,CAACmE,QAAQ;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxDnE,OAAA;cAAA+D,QAAA,EAAI5D,IAAI,CAACoE;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBnE,OAAA;cAAA+D,QAAA,GAAG,QAAM,EAAC5D,IAAI,CAACqE,IAAI;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnE,OAAA;UAAK8D,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B/D,OAAA;YAAK8D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/D,OAAA;cAAA+D,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBnE,OAAA;cACEyE,OAAO,EAAEA,CAAA,KAAM7D,YAAY,CAAC,CAACD,SAAS,CAAE;cACxCmD,SAAS,EAAC,cAAc;cAAAC,QAAA,EAEvBpD,SAAS,gBAAGX,OAAA,CAACJ,MAAM;gBAACwE,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGnE,OAAA,CAACL,GAAG;gBAACyE,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACLxD,SAAS,iBACRX,OAAA;YAAK8D,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B/D,OAAA;cAAA+D,QAAA,EAAOvD,QAAQ,CAAC;YAAC;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENnE,OAAA;UAAQyE,OAAO,EAAElE,MAAO;UAACuD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC7C/D,OAAA,CAACN,MAAM;YAAC0E,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnE,OAAA;IAAK8D,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B/D,OAAA;MAAA+D,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEvBnE,OAAA;MAAK8D,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB/D,OAAA;QACE8D,SAAS,EAAErD,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpDgE,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAAC,UAAU,CAAE;QAAAqD,QAAA,gBAExC/D,OAAA,CAACH,QAAQ;UAACuE,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAExB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnE,OAAA;QACE8D,SAAS,EAAErD,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAG;QACjDgE,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAAC,OAAO,CAAE;QAAAqD,QAAA,EACtC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnE,OAAA;QACE8D,SAAS,EAAErD,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpDgE,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAAC,UAAU,CAAE;QAAAqD,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL1D,SAAS,KAAK,UAAU,gBACvBT,OAAA;MAAK8D,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrC/D,OAAA;QAAK8D,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC/D,OAAA;UAAA+D,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BnE,OAAA;UAAA+D,QAAA,EAAG;QAAyD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChEnE,OAAA;UAAA+D,QAAA,gBACE/D,OAAA;YAAA+D,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCnE,OAAA;YAAA+D,QAAA,EAAI;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3CnE,OAAA;YAAA+D,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCnE,OAAA;YAAA+D,QAAA,EAAI;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpCnE,OAAA;YAAA+D,QAAA,EAAI;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENnE,OAAA;QACEyE,OAAO,EAAE5C,mBAAoB;QAC7B6C,QAAQ,EAAE7D,OAAQ;QAClBiD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAE9B/D,OAAA,CAACH,QAAQ;UAACuE,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrBtD,OAAO,GAAG,2BAA2B,GAAG,sCAAsC;MAAA;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAETnE,OAAA;QAAG8D,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,gBACb,eAAA/D,OAAA;UAAA+D,QAAA,EAAM;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAAAnE,OAAA;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,4DAEjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,GACJ1D,SAAS,KAAK,OAAO,gBACvBT,OAAA;MAAM2E,QAAQ,EAAE5D,SAAS,CAAC6D,YAAY,CAAClD,OAAO,CAAE;MAACoC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpE/D,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/D,OAAA;UAAA+D,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrBnE,OAAA;UACE6E,IAAI,EAAC,OAAO;UAAA,GACR9D,SAAS,CAACT,QAAQ,CAAC,OAAO,EAAE;YAAEwE,QAAQ,EAAE;UAAoB,CAAC,CAAC;UAClEC,WAAW,EAAC;QAAkB;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDpD,SAAS,CAACiE,SAAS,CAACC,MAAM,CAACV,KAAK,iBAC/BvE,OAAA;UAAM8D,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAEhD,SAAS,CAACiE,SAAS,CAACC,MAAM,CAACV,KAAK,CAAC9B;QAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACzE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnE,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/D,OAAA;UAAA+D,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxBnE,OAAA;UACE6E,IAAI,EAAC,UAAU;UAAA,GACX9D,SAAS,CAACT,QAAQ,CAAC,UAAU,EAAE;YAAEwE,QAAQ,EAAE;UAAuB,CAAC,CAAC;UACxEC,WAAW,EAAC;QAAqB;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACDpD,SAAS,CAACiE,SAAS,CAACC,MAAM,CAACC,QAAQ,iBAClClF,OAAA;UAAM8D,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAEhD,SAAS,CAACiE,SAAS,CAACC,MAAM,CAACC,QAAQ,CAACzC;QAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC5E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnE,OAAA;QAAQ6E,IAAI,EAAC,QAAQ;QAACH,QAAQ,EAAE7D,OAAQ;QAACiD,SAAS,EAAC,YAAY;QAAAC,QAAA,EAC5DlD,OAAO,GAAG,eAAe,GAAG;MAAO;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAEPnE,OAAA;MAAM2E,QAAQ,EAAE3D,YAAY,CAAC4D,YAAY,CAAChD,UAAU,CAAE;MAACkC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC1E/D,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/D,OAAA;UAAA+D,QAAA,EAAO;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1BnE,OAAA;UACE6E,IAAI,EAAC,MAAM;UAAA,GACP7D,YAAY,CAACV,QAAQ,CAAC,WAAW,EAAE;YAAEwE,QAAQ,EAAE;UAAyB,CAAC,CAAC;UAC9EC,WAAW,EAAC;QAAuB;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACDnD,YAAY,CAACgE,SAAS,CAACC,MAAM,CAACZ,SAAS,iBACtCrE,OAAA;UAAM8D,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE/C,YAAY,CAACgE,SAAS,CAACC,MAAM,CAACZ,SAAS,CAAC5B;QAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAChF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnE,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/D,OAAA;UAAA+D,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzBnE,OAAA;UACE6E,IAAI,EAAC,MAAM;UAAA,GACP7D,YAAY,CAACV,QAAQ,CAAC,UAAU,EAAE;YAAEwE,QAAQ,EAAE;UAAwB,CAAC,CAAC;UAC5EC,WAAW,EAAC;QAAsB;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,EACDnD,YAAY,CAACgE,SAAS,CAACC,MAAM,CAACX,QAAQ,iBACrCtE,OAAA;UAAM8D,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE/C,YAAY,CAACgE,SAAS,CAACC,MAAM,CAACX,QAAQ,CAAC7B;QAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnE,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/D,OAAA;UAAA+D,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrBnE,OAAA;UACE6E,IAAI,EAAC,OAAO;UAAA,GACR7D,YAAY,CAACV,QAAQ,CAAC,OAAO,EAAE;YAAEwE,QAAQ,EAAE;UAAoB,CAAC,CAAC;UACrEC,WAAW,EAAC;QAAkB;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDnD,YAAY,CAACgE,SAAS,CAACC,MAAM,CAACV,KAAK,iBAClCvE,OAAA;UAAM8D,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE/C,YAAY,CAACgE,SAAS,CAACC,MAAM,CAACV,KAAK,CAAC9B;QAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC5E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnE,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/D,OAAA;UAAA+D,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxBnE,OAAA;UACE6E,IAAI,EAAC,UAAU;UAAA,GACX7D,YAAY,CAACV,QAAQ,CAAC,UAAU,EAAE;YACpCwE,QAAQ,EAAE,sBAAsB;YAChCK,SAAS,EAAE;cAAEC,KAAK,EAAE,CAAC;cAAE3C,OAAO,EAAE;YAAyC;UAC3E,CAAC,CAAC;UACFsC,WAAW,EAAC;QAAwC;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,EACDnD,YAAY,CAACgE,SAAS,CAACC,MAAM,CAACC,QAAQ,iBACrClF,OAAA;UAAM8D,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE/C,YAAY,CAACgE,SAAS,CAACC,MAAM,CAACC,QAAQ,CAACzC;QAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnE,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/D,OAAA;UAAA+D,QAAA,EAAO;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCnE,OAAA;UACE6E,IAAI,EAAC,KAAK;UAAA,GACN7D,YAAY,CAACV,QAAQ,CAAC,OAAO,CAAC;UAClCyE,WAAW,EAAC;QAAyB;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnE,OAAA;QAAQ6E,IAAI,EAAC,QAAQ;QAACH,QAAQ,EAAE7D,OAAQ;QAACiD,SAAS,EAAC,YAAY;QAAAC,QAAA,EAC5DlD,OAAO,GAAG,gBAAgB,GAAG;MAAU;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjE,EAAA,CA3RID,WAAW;EAAA,QACsDX,OAAO,EAK1DC,OAAO,EACJA,OAAO;AAAA;AAAA8F,EAAA,GAPxBpF,WAAW;AA6RjB,eAAeA,WAAW;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}