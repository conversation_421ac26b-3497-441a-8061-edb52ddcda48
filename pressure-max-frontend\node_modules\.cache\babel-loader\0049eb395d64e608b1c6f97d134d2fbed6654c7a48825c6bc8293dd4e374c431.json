{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Delete = createLucideIcon(\"Delete\", [[\"path\", {\n  d: \"M20 5H9l-7 7 7 7h11a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2Z\",\n  key: \"1oy587\"\n}], [\"line\", {\n  x1: \"18\",\n  x2: \"12\",\n  y1: \"9\",\n  y2: \"15\",\n  key: \"1olkx5\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"18\",\n  y1: \"9\",\n  y2: \"15\",\n  key: \"1n50pc\"\n}]]);\nexport { Delete as default };", "map": {"version": 3, "names": ["Delete", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\node_modules\\lucide-react\\src\\icons\\delete.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Delete\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgNUg5bC03IDcgNyA3aDExYTIgMiAwIDAgMCAyLTJWN2EyIDIgMCAwIDAtMi0yWiIgLz4KICA8bGluZSB4MT0iMTgiIHgyPSIxMiIgeTE9IjkiIHkyPSIxNSIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxOCIgeTE9IjkiIHkyPSIxNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/delete\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Delete = createLucideIcon('Delete', [\n  ['path', { d: 'M20 5H9l-7 7 7 7h11a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2Z', key: '1oy587' }],\n  ['line', { x1: '18', x2: '12', y1: '9', y2: '15', key: '1olkx5' }],\n  ['line', { x1: '12', x2: '18', y1: '9', y2: '15', key: '1n50pc' }],\n]);\n\nexport default Delete;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,oDAAsD;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnF,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}