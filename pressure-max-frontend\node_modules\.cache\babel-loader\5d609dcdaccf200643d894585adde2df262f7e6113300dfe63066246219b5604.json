{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Warehouse = createLucideIcon(\"Warehouse\", [[\"path\", {\n  d: \"M22 8.35V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8.35A2 2 0 0 1 3.26 6.5l8-3.2a2 2 0 0 1 1.48 0l8 3.2A2 2 0 0 1 22 8.35Z\",\n  key: \"gksnxg\"\n}], [\"path\", {\n  d: \"M6 18h12\",\n  key: \"9pbo8z\"\n}], [\"path\", {\n  d: \"M6 14h12\",\n  key: \"4cwo0f\"\n}], [\"rect\", {\n  width: \"12\",\n  height: \"12\",\n  x: \"6\",\n  y: \"10\",\n  key: \"apd30q\"\n}]]);\nexport { Warehouse as default };", "map": {"version": 3, "names": ["Warehouse", "createLucideIcon", "d", "key", "width", "height", "x", "y"], "sources": ["C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\node_modules\\lucide-react\\src\\icons\\warehouse.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Warehouse\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgOC4zNVYyMGEyIDIgMCAwIDEtMiAySDRhMiAyIDAgMCAxLTItMlY4LjM1QTIgMiAwIDAgMSAzLjI2IDYuNWw4LTMuMmEyIDIgMCAwIDEgMS40OCAwbDggMy4yQTIgMiAwIDAgMSAyMiA4LjM1WiIgLz4KICA8cGF0aCBkPSJNNiAxOGgxMiIgLz4KICA8cGF0aCBkPSJNNiAxNGgxMiIgLz4KICA8cmVjdCB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHg9IjYiIHk9IjEwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/warehouse\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Warehouse = createLucideIcon('Warehouse', [\n  [\n    'path',\n    {\n      d: 'M22 8.35V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8.35A2 2 0 0 1 3.26 6.5l8-3.2a2 2 0 0 1 1.48 0l8 3.2A2 2 0 0 1 22 8.35Z',\n      key: 'gksnxg',\n    },\n  ],\n  ['path', { d: 'M6 18h12', key: '9pbo8z' }],\n  ['path', { d: 'M6 14h12', key: '4cwo0f' }],\n  ['rect', { width: '12', height: '12', x: '6', y: '10', key: 'apd30q' }],\n]);\n\nexport default Warehouse;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}