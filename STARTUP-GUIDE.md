# Pressure Max - Startup Guide

This guide explains how to start the Pressure Max application using the provided batch files.

## 🚀 Quick Start

### Option 1: Full Setup (Recommended for first time)
```bash
# Double-click or run:
start-all.bat
```

This script will:
- ✅ Check Node.js and npm installation
- 📦 Install dependencies if needed
- 📝 Create default .env file
- 🚀 Start both API and Frontend servers
- 📊 Show status and URLs

### Option 2: Quick Development Start
```bash
# Double-click or run:
start-dev.bat
```

This script will:
- 🚀 Quickly start both servers (assumes dependencies are installed)
- ⚡ Faster startup for development

### Option 3: Stop All Services
```bash
# Double-click or run:
stop-all.bat
```

This script will:
- 🛑 Stop all Node.js processes
- 📊 Show port status
- ✅ Confirm services are stopped

## 📋 Prerequisites

1. **Node.js** (v16 or higher)
   - Download from: https://nodejs.org/
   - Verify: `node --version`

2. **npm** (comes with Node.js)
   - Verify: `npm --version`

3. **PostgreSQL** (for database)
   - Download from: https://www.postgresql.org/
   - Create database: `pressure_max`

4. **Redis** (optional, for caching)
   - Download from: https://redis.io/
   - Or use cloud Redis service

## 🔧 Configuration

### Environment Variables (.env)

The `start-all.bat` script creates a default `.env` file in `pressure-max-api/`. Update it with your actual values:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pressure_max
DB_USER=postgres
DB_PASSWORD=your_password

# Redis Configuration (Optional)
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Facebook API Configuration
FACEBOOK_APP_ID=394349039883481
FACEBOOK_APP_SECRET=your-facebook-app-secret

# Server Configuration
PORT=3000
NODE_ENV=development

# Frontend URL
FRONTEND_URL=http://localhost:3001
```

## 🌐 Service URLs

After starting, the following services will be available:

| Service | URL | Description |
|---------|-----|-------------|
| Frontend | http://localhost:3001 | React application |
| Backend API | http://localhost:3000 | Express.js API server |
| API Documentation | http://localhost:3000/api-docs | Swagger UI |
| Health Check | http://localhost:3000/health | API health status |

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Check what's using the ports
   netstat -an | findstr ":3000"
   netstat -an | findstr ":3001"
   
   # Kill processes using the ports
   stop-all.bat
   ```

2. **Dependencies not installed**
   ```bash
   # Run the full setup
   start-all.bat
   ```

3. **Database connection error**
   - Ensure PostgreSQL is running
   - Check database credentials in `.env`
   - Create the database if it doesn't exist

4. **Redis connection error**
   - Redis is optional, the app will work without it
   - Install Redis if you want caching features

### Manual Installation

If the batch files don't work, you can install manually:

```bash
# Install API dependencies
cd pressure-max-api
npm install

# Install Frontend dependencies
cd ../pressure-max-frontend
npm install

# Start API (in one terminal)
cd pressure-max-api
npm run dev

# Start Frontend (in another terminal)
cd pressure-max-frontend
npm start
```

## 📝 Development Workflow

1. **First time setup:**
   ```bash
   start-all.bat
   ```

2. **Daily development:**
   ```bash
   start-dev.bat
   ```

3. **When done:**
   ```bash
   stop-all.bat
   ```

## 🔍 Monitoring

### Logs
- Each service runs in its own terminal window
- API logs show in "Pressure Max API" window
- Frontend logs show in "Pressure Max Frontend" window

### Health Checks
- API Health: http://localhost:3000/health
- Frontend: Check if http://localhost:3001 loads

### Database
- Use pgAdmin or your preferred PostgreSQL client
- Connection: localhost:5432, database: pressure_max

## 🚨 Important Notes

- **First startup** may take longer due to dependency installation
- **Keep terminal windows open** to see logs
- **Update .env file** with your actual Facebook API credentials
- **PostgreSQL must be running** for the API to work
- **Redis is optional** but recommended for better performance

## 🆘 Getting Help

If you encounter issues:

1. Check the terminal windows for error messages
2. Verify all prerequisites are installed
3. Check the `.env` file configuration
4. Try the manual installation steps
5. Check the troubleshooting section above

## 📚 Additional Resources

- [Node.js Documentation](https://nodejs.org/docs/)
- [React Documentation](https://reactjs.org/docs/)
- [Express.js Documentation](https://expressjs.com/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Facebook API Documentation](https://developers.facebook.com/docs/)
