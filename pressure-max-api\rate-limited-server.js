const express = require('express');
const cors = require('cors');
const app = express();

// Rate limiting and caching setup
const cache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache
const API_DELAY = 2000; // 2 second delay between API calls
let lastApiCall = 0;

// Rate limiting helper
async function rateLimitedApiCall(apiCallFunction) {
  const now = Date.now();
  const timeSinceLastCall = now - lastApiCall;
  
  if (timeSinceLastCall < API_DELAY) {
    const waitTime = API_DELAY - timeSinceLastCall;
    console.log(`⏱️ Rate limiting: waiting ${waitTime}ms before next API call`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }
  
  lastApiCall = Date.now();
  return await apiCallFunction();
}

// Cache helper functions
function getCacheKey(endpoint, params) {
  return `${endpoint}_${JSON.stringify(params)}`;
}

function getFromCache(key) {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log(`💾 Cache hit for ${key}`);
    return cached.data;
  }
  return null;
}

function setCache(key, data) {
  cache.set(key, {
    data: data,
    timestamp: Date.now()
  });
  console.log(`💾 Cached data for ${key}`);
}

// Middleware
app.use(cors({
  origin: 'http://localhost:3001',
  credentials: true
}));

app.use(express.json());

// Helper function to ensure ad account ID has proper format
function ensureActPrefix(adAccountId) {
  if (adAccountId.startsWith('act_')) {
    return adAccountId;
  }
  return `act_${adAccountId}`;
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// User profile endpoint with real Facebook data
app.get('/api/v1/users/profile', async (req, res) => {
  try {
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';
    
    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get('https://graph.facebook.com/v23.0/me', {
        params: {
          fields: 'id,name,email,first_name,last_name,picture',
          access_token: accessToken
        }
      });
    });

    const fbUser = response.data;
    console.log('Facebook User Profile:', fbUser);

    res.json({
      id: `fb_${fbUser.id}`,
      email: fbUser.email || '<EMAIL>',
      firstName: fbUser.first_name || 'Facebook',
      lastName: fbUser.last_name || 'User',
      name: fbUser.name,
      role: 'user',
      isActive: true,
      lastLoginAt: new Date().toISOString(),
      picture: fbUser.picture?.data?.url,
      facebookId: fbUser.id
    });
  } catch (error) {
    console.error('Facebook user profile error:', error.response?.data || error.message);
    res.json({
      id: 'user_fb_demo',
      email: '<EMAIL>',
      firstName: 'Facebook',
      lastName: 'User',
      role: 'user',
      isActive: true,
      lastLoginAt: new Date().toISOString(),
      error: 'Real API call failed, showing demo data'
    });
  }
});

// Ad accounts endpoint with caching
app.get('/api/v1/facebook/ad-accounts', async (req, res) => {
  try {
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    // Check cache first
    const cacheKey = getCacheKey('ad-accounts', {});
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      return res.json(cachedData);
    }

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get('https://graph.facebook.com/v23.0/me/adaccounts', {
        params: {
          fields: 'id,name,account_status,currency,timezone_name,business,account_id',
          access_token: accessToken,
          limit: 50
        }
      });
    });

    console.log('Facebook Ad Accounts Response:', response.data);
    const adAccounts = response.data.data || [];
    
    // Cache the result
    setCache(cacheKey, adAccounts);
    
    res.json(adAccounts);
  } catch (error) {
    console.error('Facebook ad accounts error:', error.response?.data || error.message);
    res.json([]);
  }
});

// Campaigns endpoint with caching and rate limiting (no insights for now)
app.get('/api/v1/facebook/campaigns/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🔍 Fetching campaigns for ad account:', adAccountId);

    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('📝 Using formatted ad account ID:', formattedAdAccountId);

    // Check cache first
    const cacheKey = getCacheKey('campaigns', { adAccountId: formattedAdAccountId });
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('💾 Returning cached campaigns data');
      return res.json(cachedData);
    }

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/campaigns`, {
        params: {
          fields: 'id,name,objective,status,created_time,updated_time,daily_budget,lifetime_budget,budget_remaining,configured_status,effective_status',
          access_token: accessToken,
          limit: 25 // Reduced limit to avoid rate limits
        }
      });
    });

    console.log('✅ Facebook campaigns retrieved:', response.data.data?.length || 0, 'campaigns');

    // Add basic insights structure for frontend compatibility (skip API calls for now)
    const campaigns = response.data.data || [];
    const campaignsWithBasicInsights = campaigns.map(campaign => ({
      ...campaign,
      insights: {
        impressions: '0',
        clicks: '0',
        spend: '0.00',
        cpm: '0.00',
        cpc: '0.00',
        ctr: '0.00',
        reach: '0',
        frequency: '0.00',
        actions: [],
        cost_per_action_type: []
      }
    }));

    // Cache the result
    setCache(cacheKey, campaignsWithBasicInsights);

    res.json(campaignsWithBasicInsights);
  } catch (error) {
    console.error('❌ Facebook campaigns error:', error.response?.data || error.message);
    res.json([]);
  }
});

// Campaign creation endpoint with rate limiting
app.post('/api/v1/facebook/campaigns', async (req, res) => {
  try {
    const { adAccountId, name, objective, status = 'PAUSED' } = req.body;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🚀 Creating Facebook campaign:', { adAccountId, name, objective, status });

    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('📝 Using formatted ad account ID:', formattedAdAccountId);

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/campaigns`, null, {
        params: {
          name: name,
          objective: objective,
          status: status,
          special_ad_categories: '[]',
          access_token: accessToken
        }
      });
    });

    console.log('✅ Facebook campaign created successfully:', response.data);

    // Clear campaigns cache to force refresh
    const cacheKey = getCacheKey('campaigns', { adAccountId: formattedAdAccountId });
    cache.delete(cacheKey);
    console.log('🗑️ Cleared campaigns cache');

    res.json({
      success: true,
      campaign: response.data,
      message: 'Campaign created successfully'
    });
  } catch (error) {
    console.error('❌ Facebook campaign creation error:', error.response?.data || error.message);
    
    res.json({
      error: 'Failed to create campaign in Facebook',
      details: error.response?.data || error.message,
      fallback_campaign: {
        id: `demo_${Date.now()}`,
        name: req.body.name,
        objective: req.body.objective,
        status: 'DEMO_MODE',
        created_time: new Date().toISOString(),
        note: 'Campaign creation failed, this is demo data'
      }
    });
  }
});

// Ad sets endpoint with caching and rate limiting
app.get('/api/v1/facebook/adsets/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🔍 Fetching ad sets for ad account:', adAccountId);

    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('📝 Using formatted ad account ID:', formattedAdAccountId);

    // Check cache first
    const cacheKey = getCacheKey('adsets', { adAccountId: formattedAdAccountId });
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('💾 Returning cached ad sets data');
      return res.json(cachedData);
    }

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/adsets`, {
        params: {
          fields: 'id,name,campaign_id,status,created_time,updated_time,daily_budget,lifetime_budget,budget_remaining,targeting,optimization_goal,billing_event,bid_amount,configured_status,effective_status',
          access_token: accessToken,
          limit: 25
        }
      });
    });

    console.log('✅ Facebook ad sets retrieved:', response.data.data?.length || 0, 'ad sets');

    const adSets = response.data.data || [];
    const adSetsWithBasicInsights = adSets.map(adSet => ({
      ...adSet,
      insights: {
        impressions: '0',
        clicks: '0',
        spend: '0.00',
        cpm: '0.00',
        cpc: '0.00',
        ctr: '0.00',
        reach: '0',
        frequency: '0.00',
        actions: [],
        cost_per_action_type: []
      }
    }));

    setCache(cacheKey, adSetsWithBasicInsights);
    res.json(adSetsWithBasicInsights);
  } catch (error) {
    console.error('❌ Facebook ad sets error:', error.response?.data || error.message);
    res.json([]);
  }
});

// Ads endpoint with caching and rate limiting
app.get('/api/v1/facebook/ads/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🔍 Fetching ads for ad account:', adAccountId);

    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('📝 Using formatted ad account ID:', formattedAdAccountId);

    // Check cache first
    const cacheKey = getCacheKey('ads', { adAccountId: formattedAdAccountId });
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('💾 Returning cached ads data');
      return res.json(cachedData);
    }

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/ads`, {
        params: {
          fields: 'id,name,adset_id,campaign_id,status,created_time,updated_time,creative{id,title,image_url,video_id,thumbnail_url,call_to_action,link_url},configured_status,effective_status',
          access_token: accessToken,
          limit: 25
        }
      });
    });

    console.log('✅ Facebook ads retrieved:', response.data.data?.length || 0, 'ads');

    const ads = response.data.data || [];
    const adsWithBasicInsights = ads.map(ad => ({
      ...ad,
      insights: {
        impressions: '0',
        clicks: '0',
        spend: '0.00',
        cpm: '0.00',
        cpc: '0.00',
        ctr: '0.00',
        reach: '0',
        frequency: '0.00',
        actions: [],
        cost_per_action_type: []
      }
    }));

    setCache(cacheKey, adsWithBasicInsights);
    res.json(adsWithBasicInsights);
  } catch (error) {
    console.error('❌ Facebook ads error:', error.response?.data || error.message);
    res.json([]);
  }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 Rate-Limited Pressure Max API server running on port ${PORT}`);
  console.log(`📚 Health check available at http://localhost:${PORT}/health`);
  console.log(`🔗 CORS enabled for http://localhost:3001`);
  console.log(`⏱️ API rate limiting: ${API_DELAY}ms delay between calls`);
  console.log(`💾 Cache duration: ${CACHE_DURATION / 1000}s`);
});
