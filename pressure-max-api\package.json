{"name": "pressure-max-api", "version": "1.0.0", "description": "Comprehensive API engine for Pressure Max - Facebook Ads automation and lead management platform", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "knex migrate:latest", "migrate:rollback": "knex migrate:rollback", "seed": "knex seed:run", "docs": "swagger-jsdoc -d swaggerDef.js src/routes/*.js -o docs/swagger.json", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-facebook": "^3.0.0", "knex": "^3.0.1", "pg": "^8.11.3", "redis": "^4.6.10", "socket.io": "^4.7.4", "axios": "^1.6.2", "stripe": "^14.7.0", "node-cron": "^3.0.3", "multer": "^1.4.5-lts.1", "sharp": "^0.33.0", "aws-sdk": "^2.1498.0", "nodemailer": "^6.9.7", "ical-generator": "^4.1.0", "moment": "^2.29.4", "joi": "^17.11.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["facebook-ads", "lead-management", "automation", "api", "crm", "vapi", "stripe"], "author": "Pressure Max Team", "license": "MIT"}