const logger = require('./src/config/logger');

async function debugServer() {
  try {
    console.log('1. Testing config...');
    const config = require('./src/config/config');
    console.log('✅ Config loaded');

    console.log('2. Testing database...');
    const database = require('./src/config/database');
    await database.initialize();
    console.log('✅ Database connected');

    console.log('3. Testing Redis...');
    const redis = require('./src/config/redis');
    try {
      await redis.initialize();
      console.log('✅ Redis connected');
    } catch (error) {
      console.log('⚠️ Redis failed (optional):', error.message);
    }

    console.log('4. Testing routes...');
    const facebookRoutes = require('./src/routes/facebook');
    console.log('✅ Facebook routes loaded');

    console.log('5. Testing server class...');
    const express = require('express');
    const app = express();
    console.log('✅ Express app created');

    console.log('🎉 All components loaded successfully!');
    
    // Try to start a simple server
    const server = app.listen(3000, () => {
      console.log('🚀 Test server running on port 3000');
      server.close();
    });

  } catch (error) {
    console.error('❌ Error:', error);
    console.error('Stack:', error.stack);
  }
}

debugServer();
