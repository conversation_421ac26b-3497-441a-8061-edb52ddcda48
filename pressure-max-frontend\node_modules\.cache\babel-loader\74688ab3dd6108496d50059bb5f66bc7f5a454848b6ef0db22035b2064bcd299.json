{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst GitCommitHorizontal = createLucideIcon(\"GitCommitHorizontal\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1v7zrd\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"9\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1dyftd\"\n}], [\"line\", {\n  x1: \"15\",\n  x2: \"21\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"oup4p8\"\n}]]);\nexport { GitCommitHorizontal as default };", "map": {"version": 3, "names": ["GitCommitHorizontal", "createLucideIcon", "cx", "cy", "r", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\node_modules\\lucide-react\\src\\icons\\git-commit-horizontal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name GitCommitHorizontal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgogIDxsaW5lIHgxPSIzIiB4Mj0iOSIgeTE9IjEyIiB5Mj0iMTIiIC8+CiAgPGxpbmUgeDE9IjE1IiB4Mj0iMjEiIHkxPSIxMiIgeTI9IjEyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/git-commit-horizontal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst GitCommitHorizontal = createLucideIcon('GitCommitHorizontal', [\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n  ['line', { x1: '3', x2: '9', y1: '12', y2: '12', key: '1dyftd' }],\n  ['line', { x1: '15', x2: '21', y1: '12', y2: '12', key: 'oup4p8' }],\n]);\n\nexport default GitCommitHorizontal;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,mBAAA,GAAsBC,gBAAA,CAAiB,qBAAuB,GAClE,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}