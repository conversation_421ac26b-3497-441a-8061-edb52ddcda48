{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BellRing = createLucideIcon(\"BellRing\", [[\"path\", {\n  d: \"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9\",\n  key: \"1qo2s2\"\n}], [\"path\", {\n  d: \"M10.3 21a1.94 1.94 0 0 0 3.4 0\",\n  key: \"qgo35s\"\n}], [\"path\", {\n  d: \"M4 2C2.8 3.7 2 5.7 2 8\",\n  key: \"tap9e0\"\n}], [\"path\", {\n  d: \"M22 8c0-2.3-.8-4.3-2-6\",\n  key: \"5bb3ad\"\n}]]);\nexport { BellRing as default };", "map": {"version": 3, "names": ["BellRing", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\node_modules\\lucide-react\\src\\icons\\bell-ring.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BellRing\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiA4YTYgNiAwIDAgMSAxMiAwYzAgNyAzIDkgMyA5SDNzMy0yIDMtOSIgLz4KICA8cGF0aCBkPSJNMTAuMyAyMWExLjk0IDEuOTQgMCAwIDAgMy40IDAiIC8+CiAgPHBhdGggZD0iTTQgMkMyLjggMy43IDIgNS43IDIgOCIgLz4KICA8cGF0aCBkPSJNMjIgOGMwLTIuMy0uOC00LjMtMi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/bell-ring\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BellRing = createLucideIcon('BellRing', [\n  ['path', { d: 'M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9', key: '1qo2s2' }],\n  ['path', { d: 'M10.3 21a1.94 1.94 0 0 0 3.4 0', key: 'qgo35s' }],\n  ['path', { d: 'M4 2C2.8 3.7 2 5.7 2 8', key: 'tap9e0' }],\n  ['path', { d: 'M22 8c0-2.3-.8-4.3-2-6', key: '5bb3ad' }],\n]);\n\nexport default BellRing;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAED,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,EACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}