@echo off
echo ========================================
echo    PRESSURE MAX - STARTUP SCRIPT
echo ========================================
echo.

:: Set colors for better visibility
color 0A

:: Check if Node.js is installed
echo [1/6] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js is installed

:: Check if npm is installed
echo [2/6] Checking npm installation...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: npm is not installed or not in PATH
    pause
    exit /b 1
)
echo ✅ npm is installed

:: Navigate to the project root
cd /d "%~dp0"

:: Check if directories exist
echo [3/6] Checking project structure...
if not exist "pressure-max-api" (
    echo ❌ ERROR: pressure-max-api directory not found
    pause
    exit /b 1
)
if not exist "pressure-max-frontend" (
    echo ❌ ERROR: pressure-max-frontend directory not found
    pause
    exit /b 1
)
echo ✅ Project directories found

:: Install dependencies if needed
echo [4/6] Checking and installing dependencies...

:: Check API dependencies
cd pressure-max-api
if not exist "node_modules" (
    echo 📦 Installing API dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ ERROR: Failed to install API dependencies
        pause
        exit /b 1
    )
) else (
    echo ✅ API dependencies already installed
)

:: Check Frontend dependencies
cd ..\pressure-max-frontend
if not exist "node_modules" (
    echo 📦 Installing Frontend dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ ERROR: Failed to install Frontend dependencies
        pause
        exit /b 1
    )
) else (
    echo ✅ Frontend dependencies already installed
)

cd ..

:: Create environment file if it doesn't exist
echo [5/6] Checking environment configuration...
if not exist "pressure-max-api\.env" (
    echo 📝 Creating default .env file...
    (
        echo # Database Configuration
        echo DB_HOST=localhost
        echo DB_PORT=5432
        echo DB_NAME=pressure_max
        echo DB_USER=postgres
        echo DB_PASSWORD=password
        echo.
        echo # Redis Configuration ^(Optional^)
        echo REDIS_HOST=localhost
        echo REDIS_PORT=6379
        echo.
        echo # JWT Configuration
        echo JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
        echo.
        echo # Facebook API Configuration
        echo FACEBOOK_APP_ID=394349039883481
        echo FACEBOOK_APP_SECRET=your-facebook-app-secret
        echo.
        echo # Server Configuration
        echo PORT=3000
        echo NODE_ENV=development
        echo.
        echo # Frontend URL
        echo FRONTEND_URL=http://localhost:3001
    ) > pressure-max-api\.env
    echo ✅ Default .env file created
    echo ⚠️  Please update the .env file with your actual credentials
) else (
    echo ✅ Environment file exists
)

:: Start the services
echo [6/6] Starting services...
echo.
echo 🚀 Starting Pressure Max Application...
echo.
echo 📋 Services will start in the following order:
echo    1. Backend API Server (Port 3000)
echo    2. Frontend React App (Port 3001)
echo.
echo 💡 TIP: Keep this window open to see all logs
echo 💡 TIP: Press Ctrl+C to stop all services
echo.

:: Start API server in a new window
echo 🔧 Starting Backend API Server...
start "Pressure Max API" cmd /k "cd /d %~dp0pressure-max-api && echo Starting API Server on http://localhost:3000 && echo API Documentation: http://localhost:3000/api-docs && echo. && npm run dev"

:: Wait a moment for API to start
timeout /t 3 /nobreak >nul

:: Start Frontend in a new window
echo 🎨 Starting Frontend React App...
start "Pressure Max Frontend" cmd /k "cd /d %~dp0pressure-max-frontend && echo Starting Frontend on http://localhost:3001 && echo. && npm start"

:: Wait a moment for frontend to start
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo    🎉 PRESSURE MAX STARTED SUCCESSFULLY!
echo ========================================
echo.
echo 📱 Frontend: http://localhost:3001
echo 🔧 Backend API: http://localhost:3000
echo 📚 API Docs: http://localhost:3000/api-docs
echo 🏥 Health Check: http://localhost:3000/health
echo.
echo 📝 Logs are shown in separate windows
echo 🛑 To stop all services: Close the terminal windows or press Ctrl+C in each
echo.
echo ⚠️  IMPORTANT NOTES:
echo    - Make sure PostgreSQL is running if using a local database
echo    - Redis is optional but recommended for caching
echo    - Update the .env file with your actual Facebook API credentials
echo    - The first startup might take longer due to dependency installation
echo.

:: Keep this window open to show status
echo 📊 Monitoring services... (Press any key to close this status window)
pause >nul

echo.
echo 👋 Startup script completed. Services are running in separate windows.
echo.
