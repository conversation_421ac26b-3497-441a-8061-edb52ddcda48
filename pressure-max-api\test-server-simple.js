const express = require('express');
const http = require('http');
const cors = require('cors');

console.log('🧪 Starting simple test server...');

const app = express();

// Configure server with larger header limits
const server = http.createServer({
  maxHeaderSize: 16384, // 16KB header limit
  requestTimeout: 30000,
  headersTimeout: 30000, // Must be <= requestTimeout
  keepAliveTimeout: 5000
}, app);

// Handle server errors
server.on('error', (error) => {
  console.error('❌ Server error:', error);
});

// Handle client errors (including header overflow)
server.on('clientError', (err, socket) => {
  if (err.code === 'HPE_HEADER_OVERFLOW') {
    console.warn('⚠️ Client sent headers too large');
    socket.end('HTTP/1.1 431 Request Header Fields Too Large\r\n\r\n');
  } else {
    console.warn('⚠️ Client error:', err.message);
    socket.destroy();
  }
});

// Basic middleware
app.use(cors({
  origin: 'http://localhost:3001',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(express.json({ limit: '10mb' }));

// Handle 431 errors specifically
app.use((err, req, res, next) => {
  if (err.code === 'HPE_HEADER_OVERFLOW' || err.status === 431) {
    console.warn('⚠️ Request headers too large for:', req.url);
    return res.status(431).json({
      error: 'Request Header Fields Too Large',
      message: 'Please clear your browser cookies and try again.',
      code: 'HEADER_TOO_LARGE'
    });
  }
  next(err);
});

// Test routes
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'Simple test server is running'
  });
});

app.get('/test', (req, res) => {
  res.json({
    message: 'Test endpoint working',
    headers: Object.keys(req.headers),
    headerCount: Object.keys(req.headers).length
  });
});

// Facebook OAuth test endpoint
app.get('/api/v1/facebook/oauth-url', (req, res) => {
  res.json({
    success: true,
    message: 'OAuth URL endpoint working',
    redirectUri: req.query.redirectUri
  });
});

// Start server
const PORT = 3000;
server.listen(PORT, () => {
  console.log(`✅ Simple test server running on http://localhost:${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/test`);
  console.log(`🔗 OAuth test: http://localhost:${PORT}/api/v1/facebook/oauth-url?redirectUri=test`);
  console.log('');
  console.log('💡 If this works, the issue is in the main server configuration');
  console.log('💡 If this fails, the issue is with headers/cookies');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
