import React, { useState, useEffect } from 'react';
import { facebookAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';
import { Facebook, Users, CreditCard, AlertCircle, CheckCircle } from 'lucide-react';

const FacebookSection = () => {
  const { isAuthenticated } = useAuth();
  const [facebookStatus, setFacebookStatus] = useState({
    connected: false,
    loading: false,
    accounts: [],
    pages: [],
    error: null
  });

  useEffect(() => {
    if (isAuthenticated) {
      loadFacebookData();
    }
  }, [isAuthenticated]);

  const loadFacebookData = async () => {
    try {
      setFacebookStatus(prev => ({ ...prev, loading: true, error: null }));
      
      const [accountsResponse, pagesResponse] = await Promise.all([
        facebookAPI.getAdAccounts().catch(() => ({ data: [] })),
        facebookAPI.getPages().catch(() => ({ data: [] }))
      ]);

      setFacebookStatus(prev => ({
        ...prev,
        connected: accountsResponse.data.length > 0 || pagesResponse.data.length > 0,
        accounts: accountsResponse.data || [],
        pages: pagesResponse.data || [],
        loading: false
      }));
    } catch (error) {
      setFacebookStatus(prev => ({
        ...prev,
        loading: false,
        error: error.response?.data?.message || 'Failed to load Facebook data'
      }));
    }
  };

  const initiateOAuth = async () => {
    try {
      setFacebookStatus(prev => ({ ...prev, loading: true }));
      
      const redirectUri = `${window.location.origin}/facebook-callback`;
      const response = await facebookAPI.getOAuthUrl(redirectUri);
      
      if (response.data.oauthUrl) {
        window.location.href = response.data.oauthUrl;
      } else {
        throw new Error('No OAuth URL received');
      }
    } catch (error) {
      setFacebookStatus(prev => ({ ...prev, loading: false }));
      toast.error(error.response?.data?.message || 'Failed to initiate Facebook OAuth');
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="facebook-section">
        <h2>Facebook Integration</h2>
        <div className="auth-required">
          <AlertCircle size={20} />
          <p>Please log in to connect your Facebook account</p>
        </div>
      </div>
    );
  }

  return (
    <div className="facebook-section">
      <h2>Facebook Integration</h2>
      
      <div className="facebook-status">
        <div className="status-header">
          <Facebook size={20} />
          <span>Connection Status</span>
          {facebookStatus.connected ? (
            <CheckCircle size={16} className="status-icon connected" />
          ) : (
            <AlertCircle size={16} className="status-icon disconnected" />
          )}
        </div>
        
        <p className={`status-text ${facebookStatus.connected ? 'connected' : 'disconnected'}`}>
          {facebookStatus.connected ? 'Connected to Facebook' : 'Not connected to Facebook'}
        </p>

        {facebookStatus.error && (
          <div className="error-message">
            <AlertCircle size={16} />
            <span>{facebookStatus.error}</span>
          </div>
        )}
      </div>

      {!facebookStatus.connected && (
        <div className="oauth-section">
          <p>Connect your Facebook Business Manager account to start managing campaigns.</p>
          <button 
            onClick={initiateOAuth}
            disabled={facebookStatus.loading}
            className="oauth-btn"
          >
            <Facebook size={16} />
            {facebookStatus.loading ? 'Connecting...' : 'Connect Facebook Account'}
          </button>
        </div>
      )}

      {facebookStatus.connected && (
        <div className="facebook-data">
          <div className="data-section">
            <h3>
              <CreditCard size={16} />
              Ad Accounts ({facebookStatus.accounts.length})
            </h3>
            {facebookStatus.accounts.length > 0 ? (
              <div className="accounts-list">
                {facebookStatus.accounts.map((account) => {
                  // Convert numeric account status to readable string
                  const getAccountStatus = (status) => {
                    switch(status) {
                      case 1: return { text: 'ACTIVE', class: 'active' };
                      case 2: return { text: 'DISABLED', class: 'disabled' };
                      case 3: return { text: 'UNSETTLED', class: 'unsettled' };
                      case 7: return { text: 'PENDING_RISK_REVIEW', class: 'pending' };
                      case 8: return { text: 'PENDING_SETTLEMENT', class: 'pending' };
                      case 9: return { text: 'IN_GRACE_PERIOD', class: 'grace' };
                      case 100: return { text: 'PENDING_CLOSURE', class: 'pending' };
                      case 101: return { text: 'CLOSED', class: 'closed' };
                      case 201: return { text: 'ANY_ACTIVE', class: 'active' };
                      case 202: return { text: 'ANY_CLOSED', class: 'closed' };
                      default: return { text: `STATUS_${status}`, class: 'unknown' };
                    }
                  };

                  const statusInfo = getAccountStatus(account.account_status);

                  return (
                    <div key={account.id} className="account-item">
                      <div className="account-info">
                        <strong>{account.name}</strong>
                        <span className="account-id">ID: {account.account_id}</span>
                        <span className={`account-status ${statusInfo.class}`}>
                          {statusInfo.text}
                        </span>
                      </div>
                      <div className="account-details">
                        <span>Currency: {account.currency}</span>
                        <span>Timezone: {account.timezone_name}</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <p className="no-data">No ad accounts found</p>
            )}
          </div>

          <div className="data-section">
            <h3>
              <Users size={16} />
              Pages ({facebookStatus.pages.length})
            </h3>
            {facebookStatus.pages.length > 0 ? (
              <div className="pages-list">
                {facebookStatus.pages.map((page) => (
                  <div key={page.id} className="page-item">
                    <div className="page-info">
                      <strong>{page.name}</strong>
                      <span className="page-id">ID: {page.id}</span>
                      <span className="page-category">{page.category}</span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="no-data">No pages found</p>
            )}
          </div>

          <button 
            onClick={loadFacebookData}
            disabled={facebookStatus.loading}
            className="refresh-btn"
          >
            {facebookStatus.loading ? 'Refreshing...' : 'Refresh Data'}
          </button>
        </div>
      )}
    </div>
  );
};

export default FacebookSection;
