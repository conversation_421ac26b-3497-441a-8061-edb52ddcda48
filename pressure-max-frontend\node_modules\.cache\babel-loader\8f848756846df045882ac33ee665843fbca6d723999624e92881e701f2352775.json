{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Sliders = createLucideIcon(\"Sliders\", [[\"line\", {\n  x1: \"4\",\n  x2: \"4\",\n  y1: \"21\",\n  y2: \"14\",\n  key: \"1p332r\"\n}], [\"line\", {\n  x1: \"4\",\n  x2: \"4\",\n  y1: \"10\",\n  y2: \"3\",\n  key: \"gb41h5\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"21\",\n  y2: \"12\",\n  key: \"hf2csr\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"8\",\n  y2: \"3\",\n  key: \"1kfi7u\"\n}], [\"line\", {\n  x1: \"20\",\n  x2: \"20\",\n  y1: \"21\",\n  y2: \"16\",\n  key: \"1lhrwl\"\n}], [\"line\", {\n  x1: \"20\",\n  x2: \"20\",\n  y1: \"12\",\n  y2: \"3\",\n  key: \"16vvfq\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"6\",\n  y1: \"14\",\n  y2: \"14\",\n  key: \"1uebub\"\n}], [\"line\", {\n  x1: \"10\",\n  x2: \"14\",\n  y1: \"8\",\n  y2: \"8\",\n  key: \"1yglbp\"\n}], [\"line\", {\n  x1: \"18\",\n  x2: \"22\",\n  y1: \"16\",\n  y2: \"16\",\n  key: \"1jxqpz\"\n}]]);\nexport { Sliders as default };", "map": {"version": 3, "names": ["Sliders", "createLucideIcon", "x1", "x2", "y1", "y2", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\node_modules\\lucide-react\\src\\icons\\sliders.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Sliders\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iNCIgeDI9IjQiIHkxPSIyMSIgeTI9IjE0IiAvPgogIDxsaW5lIHgxPSI0IiB4Mj0iNCIgeTE9IjEwIiB5Mj0iMyIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIxIiB5Mj0iMTIiIC8+CiAgPGxpbmUgeDE9IjEyIiB4Mj0iMTIiIHkxPSI4IiB5Mj0iMyIgLz4KICA8bGluZSB4MT0iMjAiIHgyPSIyMCIgeTE9IjIxIiB5Mj0iMTYiIC8+CiAgPGxpbmUgeDE9IjIwIiB4Mj0iMjAiIHkxPSIxMiIgeTI9IjMiIC8+CiAgPGxpbmUgeDE9IjIiIHgyPSI2IiB5MT0iMTQiIHkyPSIxNCIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxNCIgeTE9IjgiIHkyPSI4IiAvPgogIDxsaW5lIHgxPSIxOCIgeDI9IjIyIiB5MT0iMTYiIHkyPSIxNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/sliders\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sliders = createLucideIcon('Sliders', [\n  ['line', { x1: '4', x2: '4', y1: '21', y2: '14', key: '1p332r' }],\n  ['line', { x1: '4', x2: '4', y1: '10', y2: '3', key: 'gb41h5' }],\n  ['line', { x1: '12', x2: '12', y1: '21', y2: '12', key: 'hf2csr' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '3', key: '1kfi7u' }],\n  ['line', { x1: '20', x2: '20', y1: '21', y2: '16', key: '1lhrwl' }],\n  ['line', { x1: '20', x2: '20', y1: '12', y2: '3', key: '16vvfq' }],\n  ['line', { x1: '2', x2: '6', y1: '14', y2: '14', key: '1uebub' }],\n  ['line', { x1: '10', x2: '14', y1: '8', y2: '8', key: '1yglbp' }],\n  ['line', { x1: '18', x2: '22', y1: '16', y2: '16', key: '1jxqpz' }],\n]);\n\nexport default Sliders;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}