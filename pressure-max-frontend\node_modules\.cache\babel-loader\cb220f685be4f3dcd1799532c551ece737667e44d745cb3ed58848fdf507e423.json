{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Gem = createLucideIcon(\"Gem\", [[\"path\", {\n  d: \"M6 3h12l4 6-10 13L2 9Z\",\n  key: \"1pcd5k\"\n}], [\"path\", {\n  d: \"M11 3 8 9l4 13 4-13-3-6\",\n  key: \"1fcu3u\"\n}], [\"path\", {\n  d: \"M2 9h20\",\n  key: \"16fsjt\"\n}]]);\nexport { Gem as default };", "map": {"version": 3, "names": ["Gem", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\node_modules\\lucide-react\\src\\icons\\gem.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Gem\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAzaDEybDQgNi0xMCAxM0wyIDlaIiAvPgogIDxwYXRoIGQ9Ik0xMSAzIDggOWw0IDEzIDQtMTMtMy02IiAvPgogIDxwYXRoIGQ9Ik0yIDloMjAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/gem\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Gem = createLucideIcon('Gem', [\n  ['path', { d: 'M6 3h12l4 6-10 13L2 9Z', key: '1pcd5k' }],\n  ['path', { d: 'M11 3 8 9l4 13 4-13-3-6', key: '1fcu3u' }],\n  ['path', { d: 'M2 9h20', key: '16fsjt' }],\n]);\n\nexport default Gem;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,GAAA,GAAMC,gBAAA,CAAiB,KAAO,GAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}