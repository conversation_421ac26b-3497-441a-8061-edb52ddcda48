{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Skull = createLucideIcon(\"<PERSON>\", [[\"circle\", {\n  cx: \"9\",\n  cy: \"12\",\n  r: \"1\",\n  key: \"1vctgf\"\n}], [\"circle\", {\n  cx: \"15\",\n  cy: \"12\",\n  r: \"1\",\n  key: \"1tmaij\"\n}], [\"path\", {\n  d: \"M8 20v2h8v-2\",\n  key: \"ded4og\"\n}], [\"path\", {\n  d: \"m12.5 17-.5-1-.5 1h1z\",\n  key: \"3me087\"\n}], [\"path\", {\n  d: \"M16 20a2 2 0 0 0 1.56-3.25 8 8 0 1 0-11.12 0A2 2 0 0 0 8 20\",\n  key: \"xq9p5u\"\n}]]);\nexport { <PERSON> as default };", "map": {"version": 3, "names": ["Skull", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\node_modules\\lucide-react\\src\\icons\\skull.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Skull\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iMTIiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iMTUiIGN5PSIxMiIgcj0iMSIgLz4KICA8cGF0aCBkPSJNOCAyMHYyaDh2LTIiIC8+CiAgPHBhdGggZD0ibTEyLjUgMTctLjUtMS0uNSAxaDF6IiAvPgogIDxwYXRoIGQ9Ik0xNiAyMGEyIDIgMCAwIDAgMS41Ni0zLjI1IDggOCAwIDEgMC0xMS4xMiAwQTIgMiAwIDAgMCA4IDIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/skull\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Skull = createLucideIcon('Skull', [\n  ['circle', { cx: '9', cy: '12', r: '1', key: '1vctgf' }],\n  ['circle', { cx: '15', cy: '12', r: '1', key: '1tmaij' }],\n  ['path', { d: 'M8 20v2h8v-2', key: 'ded4og' }],\n  ['path', { d: 'm12.5 17-.5-1-.5 1h1z', key: '3me087' }],\n  ['path', { d: 'M16 20a2 2 0 0 0 1.56-3.25 8 8 0 1 0-11.12 0A2 2 0 0 0 8 20', key: 'xq9p5u' }],\n]);\n\nexport default Skull;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAEC,CAAA,EAAG,uBAAyB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAEC,CAAA,EAAG,6DAA+D;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC7F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}